# RPG 任務 10：數據倉庫層 (Repositories)

本文檔詳細列出了 RPG 系統數據倉庫層的實現任務。數據倉庫層負責封裝數據庫交互邏輯，為應用服務層提供清晰、面向領域對象的數據訪問接口。

**參考設計文檔：**
*   `RPG_01_System_Architecture.md` (II. 層次結構 - Infrastructure Layer, Repository)
*   `RPG_03_Database_Schema.md` (所有相關的數據庫表結構)
*   `RPG_TASK_01_Project_Setup_and_Config_System.md` (已創建的倉庫文件存根)
*   `RPG_TASK_02_Database_Schema_and_Migrations.md` (最終確認的表和字段名)

**目標位置：** `rpg_system/repositories/` 目錄下的各倉庫文件。
**數據庫操作：** 假設使用 `asyncpg` 或類似的異步庫與 PostgreSQL 交互，並有一個共享的數據庫連接池或獲取連接的機制。

## 1. Repository 基類 (可選)

- [ ] (可選) 創建一個 `BaseRepository` 類，封裝通用的數據庫連接獲取、事務管理等邏輯。
    - [ ] `def __init__(self, db_pool: asyncpg.Pool)` (或其他連接機制)
    - [ ] 提供執行查詢、獲取單行、獲取多行等輔助方法。

## 2. `PlayerCollectionRPGRepository`

在 `rpg_system/repositories/player_collection_rpg_repository.py` 中實現。
負責與 `gacha_user_collections` 表中 RPG 相關字段的交互。

- [ ] **定義 `PlayerCardData` (或 `PlayerCollectionRPGData`) Pydantic 模型或數據類：**
    - [ ] 用於表示從 `gacha_user_collections` 表查詢出的一條完整 RPG 卡牌數據記錄，包含 `id`, `user_id`, `card_id`, `rpg_level`, `rpg_xp`, `star_level`, `equipped_active_skill_ids` (解析為 List[Optional[str]]), `equipped_common_passives` (解析為 Dict[str, Optional[Dict[str, Any]]]) 等字段。
- [ ] **定義 `PlayerCollectionRPGRepository` 類 (可繼承 `BaseRepository`)：**
    - [ ] **依賴注入 (`__init__`)：** 數據庫連接池/工廠。
    - [ ] **主要方法：**
        - [ ] `async def get_player_card_by_collection_id(self, collection_id: int) -> Optional[PlayerCardData]`:
            *   根據 `gacha_user_collections.id` 查詢卡牌數據。
            *   處理 JSON 字段的解析。
        - [ ] `async def get_player_cards_by_user_id(self, user_id: int) -> List[PlayerCardData]`:
            *   查詢某用戶擁有的所有卡牌的 RPG 數據。
        - [ ] `async def update_player_card_rpg_stats(self, collection_id: int, rpg_level: int, rpg_xp: int, star_level: int) -> bool`:
            *   更新指定卡牌的 RPG 等級、經驗和星級。
        - [ ] `async def update_player_card_active_skills(self, collection_id: int, equipped_active_skill_ids: List[Optional[str]]) -> bool`:
            *   更新 `equipped_active_skill_ids` (JSON 數組)。將 Python 列表轉換為 JSON 字符串存儲。
        - [ ] `async def update_player_card_passive_skills(self, collection_id: int, equipped_common_passives: Dict[str, Optional[Dict[str, Any]]]) -> bool`:
            *   更新 `equipped_common_passives` (JSONB)。將 Python 字典轉換為 JSON 存儲。
            *   `equipped_common_passives` 的結構示例: `{"slot_0": {"skill_id": "passive_A", "level": 1}, "slot_1": null}`
        - [ ] `async def add_rpg_data_to_new_card(self, collection_id: int, card_config: 'CardConfig', default_passives_config: Optional[List['DefaultPassiveSlot']]) -> bool`:
            *   當玩家獲得新卡時 (由外部邏輯判斷)，此方法用於初始化該卡在 `gacha_user_collections` 中的 RPG 相關字段。
            *   設置默認 `rpg_level=1`, `rpg_xp=0`, `star_level=0`。
            *   根據 `card_config.default_active_skill_slot_X_id` 初始化 `equipped_active_skill_ids`。
            *   根據 `card_config.default_passives_on_acquire` (結合 `default_passives_config`) 初始化 `equipped_common_passives`。
            *   這可能是一個 `UPDATE` 語句，假設記錄已由核心gacha系統創建，但RPG字段為空/默認。

## 3. `GlobalSkillRepository`

在 `rpg_system/repositories/global_skill_repository.py` 中實現。
負責與 `gacha_user_learned_global_skills` 表的交互。

- [ ] **定義 `LearnedGlobalSkillData` Pydantic 模型或數據類：**
    - [ ] 對應 `gacha_user_learned_global_skills` 表的字段: `user_id`, `skill_id`, `skill_type`, `skill_level`, `skill_xp`, `unlocked_at`。
- [ ] **定義 `GlobalSkillRepository` 類 (可繼承 `BaseRepository`)：**
    - [ ] **依賴注入 (`__init__`)：** 數據庫連接池/工廠。
    - [ ] **主要方法：**
        - [ ] `async def get_learned_skill(self, user_id: int, skill_id: str, skill_type: Literal["ACTIVE", "PASSIVE"]) -> Optional[LearnedGlobalSkillData]`:
            *   查詢特定用戶學習的特定技能。
        - [ ] `async def get_all_learned_skills_by_user_id(self, user_id: int, skill_type: Optional[Literal["ACTIVE", "PASSIVE"]] = None) -> List[LearnedGlobalSkillData]`:
            *   查詢用戶學習的所有技能，可選按技能類型過濾。
        - [ ] `async def add_learned_skill(self, user_id: int, skill_id: str, skill_type: Literal["ACTIVE", "PASSIVE"], initial_level: int = 1, initial_xp: int = 0) -> Optional[LearnedGlobalSkillData]`:
            *   向表中插入一條新技能學習記錄。成功則返回創建的記錄。
        - [ ] `async def update_learned_skill_progress(self, user_id: int, skill_id: str, skill_type: Literal["ACTIVE", "PASSIVE"], new_level: int, new_xp: int) -> bool`:
            *   更新已學習技能的等級和經驗。
        - [ ] `async def delete_learned_skill(self, user_id: int, skill_id: str, skill_type: Literal["ACTIVE", "PASSIVE"]) -> bool`:
            *   (可選，如果設計需要遺忘技能的功能)

## 4. `UserProgressRPGRepository`

在 `rpg_system/repositories/user_progress_rpg_repository.py` 中實現。
負責與 `rpg_user_progress` 表的交互。

- [ ] **定義 `UserProgressData` Pydantic 模型或數據類：**
    - [ ] 對應 `rpg_user_progress` 表的字段: `user_id`, `current_floor_unlocked`, `current_floor_wins`, `max_floor_cleared`, `current_team_formation` (解析為 List[Optional[int]]，存儲 `gacha_user_collections.id`)。
- [ ] **定義 `UserProgressRPGRepository` 類 (可繼承 `BaseRepository`)：**
    - [ ] **依賴注入 (`__init__`)：** 數據庫連接池/工廠。
    - [ ] **主要方法：**
        - [ ] `async def get_user_progress(self, user_id: int) -> Optional[UserProgressData]`:
            *   查詢用戶的 PVE 進度。
            *   如果記錄不存在，則創建並返回一個默認進度記錄 (e.g., floor 1 unlocked, 0 wins)。
        - [ ] `async def create_user_progress(self, user_id: int) -> UserProgressData`:
            *   為新用戶創建默認的進度記錄。
        - [ ] `async def update_user_progress(self, user_progress_data: UserProgressData) -> bool`:
            *   使用傳入的 `UserProgressData` 對象完整更新用戶的PVE進度記錄。
        - [ ] `async def update_team_formation(self, user_id: int, team_formation_collection_ids: List[Optional[int]]) -> bool`:
            *   單獨更新用戶的隊伍編成 (`current_team_formation` JSONB 字段)。

## 5. SQL查詢編寫與執行

- [ ] 對於每個倉庫方法，編寫對應的 SQL 查詢語句。
- [ ] **使用參數化查詢** 以防止 SQL 注入。
- [ ] 處理數據庫操作可能拋出的異常 (e.g., `asyncpg.exceptions.UniqueViolationError`) 並適當轉換或記錄。
- [ ] 確保異步方法的正確使用 (`async`/`await`)。
- [ ] 對於 JSON/JSONB 字段的讀取和寫入，確保 Python 數據結構與數據庫 JSON 格式之間的正確轉換。
    -   寫入：`json.dumps()`
    -   讀取：`json.loads()` (如果數據庫驅動沒有自動處理)

## 6. 單元測試

- [ ] **為每個倉庫類編寫單元測試：**
    - [ ] **Mock 數據庫連接/池：** 使用 `unittest.mock` 來模擬數據庫交互，而不是實際連接數據庫。
        *   Mock `pool.acquire()` / `conn.fetchrow()` / `conn.fetch()` / `conn.execute()` 等方法。
        *   讓這些 mock 方法返回預期的數據或模擬數據庫操作的副作用。
    - [ ] **測試每個公開方法：**
        - [ ] **數據檢索方法 (getters)：**
            *   測試成功獲取數據並正確轉換為 Pydantic 模型/數據類的情況。
            *   測試未找到數據時返回 `None` 或空列表的情況。
        - [ ] **數據修改方法 (updates, inserts, deletes)：**
            *   驗證是否生成了正確的 SQL 查詢和參數。
            *   驗證是否正確調用了數據庫執行方法。
            *   測試成功操作返回 `True` 或預期對象。
            *   測試數據庫操作失敗 (e.g., mock 拋出異常) 時的行為。
        - [ ] **JSON/JSONB 字段處理：** 專門測試這些字段的序列化和反序列化是否按預期工作。
        - [ ] **邊界情況：** 如傳入無效 ID，空列表等。

## 7. 文檔與類型提示

- [ ] 為所有倉庫類及其公共方法添加清晰的文檔字符串，說明其職責、參數和返回值。
- [ ] 使用 Python 的類型提示，包括異步方法 (`async def ... -> Coroutine[...]`) 和 Pydantic 模型。

---
完成所有上述任務後，請勾選並更新 `RPG_IMPLEMENTATION_PLAN.md` 中的相應條目。 