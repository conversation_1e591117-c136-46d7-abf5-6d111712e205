# RPG 任務 03：公式求值引擎

本文檔詳細列出了 RPG 系統中公式求值引擎的實現任務。

**參考設計文檔：**
*   `RPG_09_Formula_Engine_Details.md`
*   `RPG_02_Configuration_Files.md` (確認公式中可能使用的變量來源，如 `skill_level`, `star_level`)
*   規則：**Formula Engine: Use asteval, forbid eval() and random funcs**

## 1. 選擇並集成求值庫

- [ ] **確認使用 `asteval` 庫：** 根據設計規則，我們將使用 `asteval` 庫。
- [ ] **將 `asteval` 添加到項目依賴：**
    - [ ] 更新 `requirements.txt` (或 `pyproject.toml` 如果使用 Poetry/PDM) 添加 `asteval`。
    - [ ] 執行 `pip install asteval` 或相應的依賴管理工具命令。

## 2. FormulaEvaluator 類實現

在 `rpg_system/formula_engine/evaluator.py` 中實現 `FormulaEvaluator` 類。

- [ ] **創建 `FormulaEvaluator` 類：**
    - [ ] `__init__(self)` 方法：
        - [ ] 初始化 `asteval.Interpreter()` 實例 (e.g., `self.interpreter = Interpreter()`).
        - [ ] **重要：** 根據 `RPG_09_Formula_Engine_Details.md` (2.4. 函數 和 隨機函數部分) 和 **禁用隨機函數** 的規則，配置 `asteval.Interpreter` 實例 (`symtable`)：
            - [ ] 移除或確保不包含任何不安全的內置函數 (asteval 默認比較安全，但應複查)。
            - [ ] **明確禁用隨機函數**：如 `rand()`, `random()`, `randint()`, `uniform()` 等。確保它們不在 `symtable` 中或被設置為 `None`。
            - [ ] 預先定義 `RPG_09_Formula_Engine_Details.md` (2.4. 函數) 中列出的**所有數學函數** (`min`, `max`, `abs`, `floor`, `ceil`, `round`, `sqrt`, `pow`, `ln`, `log10`, `log`, `clamp`) 到 `symtable`。
            - [ ] 預先定義 `RPG_09_Formula_Engine_Details.md` (2.4. 函數) 中列出的**邏輯函數** (`if`) 到 `symtable`。
    - [ ] `evaluate(self, formula_string: str, context_vars: Dict[str, Any]) -> Any` 方法：
        - [ ] **參數：**
            - `formula_string`: 要計算的公式字符串。
            - `context_vars`: 一個字典，包含該公式執行時所有可用的變量及其值 (e.g., `{"caster_star_level": 5, "skill_level": 10, ...}`).
        - [ ] **實現邏輯：**
            - [ ] 清理/更新 `self.interpreter.symtable`：對於 `context_vars` 中的每一個鍵值對，將其設置到 `symtable` 中。注意，這裡要小心處理，避免 `context_vars` 中的鍵覆寫了預定義的函數。一個好的做法是每次求值前創建一個臨時的符號表副本，或者在求值後清理上下文變量。
                *   更好的做法：`self.interpreter.symtable.update(context_vars)` 可以在求值前臨時加入上下文變量。
            - [ ] 使用 `self.interpreter.eval(formula_string)` 或 `self.interpreter.interpret(formula_string)` 執行求值。
            - [ ] **錯誤處理：**
                - [ ] 捕獲 `asteval` 可能拋出的異常 (例如語法錯誤、求值錯誤)。
                - [ ] 記錄詳細的錯誤日誌 (包括公式本身、上下文變量和錯誤信息)。
                - [ ] 根據 `RPG_09_Formula_Engine_Details.md` (4. 錯誤處理) 的建議，返回一個預定義的錯誤值 (e.g., `0`, `None`, 或一個特定的錯誤標記對象) 或向上拋出一個自定義的 `FormulaEvaluationError` 異常。
            - [ ] 返回計算結果。
            - [ ] **求值後清理** (重要)：確保在 `evaluate` 方法結束前，從 `self.interpreter.symtable` 中移除本次求值臨時添加的 `context_vars`，以避免污染下一次對 `evaluate` 的調用。如果 `asteval` 的 `eval()` 或 `interpret()` 有選項可以在單次求值中使用臨時符號表，則優先使用。
                *   或者，每次 `evaluate` 時，都基於一個乾淨的初始 `symtable` 創建一個新的 `Interpreter` 實例或一個新的 `symtable` 副本，然後加入 `context_vars`。這樣可以保證隔離性，但可能略有性能開銷。對於RPG的頻率，這可能是更安全的選擇。
                    *   **修正/確認**: `asteval` 的 `interpreter.symtable` 是可以動態修改的。標準做法是在求值前將上下文變量添加到 `symtable`，求值後可以選擇性地移除它們。或者，如果 `Interpreter` 實例是短生命週期的 (例如，在每個需要求值的服務方法中創建)，則不需要顯式清理。
                    *   **推薦方案：** 為了簡單和安全，可以在 `evaluate` 方法開始時，先將 `context_vars` 中的鍵從 `self.interpreter.symtable` 中 `pop` 出來 (如果存在)，然後在 `finally` 塊中確保它們被恢復或清除，或者更簡單地，每次 `evaluate` 時創建一個 `symtable` 的淺拷貝，修改拷貝，然後傳遞給 `asteval` 的求值函數（如果 `asteval` 支持傳遞臨時 `symtable` 的話，否則還是直接修改後清理）。
                    *   **最佳實踐 for `asteval`**: `asteval.eval(expression, symtable=temporary_symtable)` 允許傳入一個完整的 `symtable`。所以可以在 `evaluate` 方法中，先複製 `self.interpreter.symtable` (基礎函數表)，然後 `.update(context_vars)`，再將此臨時表傳給 `asteval.eval()`。

## 3. 單元測試

為 `FormulaEvaluator` 創建單元測試。

- [ ] **測試文件位置：** (例如 `tests/rpg_system/formula_engine/test_evaluator.py`)
- [ ] **測試用例：**
    - [ ] **基本算術運算：** 加、減、乘、除、冪、括號。
    - [ ] **變量替換：** 測試從 `context_vars` 正確獲取變量值。
    - [ ] **函數調用：**
        - [ ] 測試所有預定義的數學函數 (`min`, `max`, `abs`, `floor`, `ceil`, `round`, `sqrt`, `pow`, `ln`, `log10`, `log`, `clamp`)。
        - [ ] 測試邏輯函數 (`if`)。
    - [ ] **邏輯運算符：** `>`, `<`, `>=`, `<=`, `==`, `!=`, `&&` (或 `and`), `||` (或 `or`)。
    - [ ] **混合複雜公式：** 結合變量、運算符和函數的複雜表達式。
    - [ ] **邊界情況：**
        - [ ] 除以零 (預期行為，例如返回0或None並記錄錯誤)。
        - [ ] `sqrt(-1)` (預期行為)。
        - [ ] 空公式字符串。
        - [ ] 包含未定義變量的公式 (預期行為，例如變量視為0或報錯)。
    - [ ] **錯誤處理測試：**
        - [ ] 語法錯誤的公式 (預期捕獲異常或返回錯誤值)。
        - [ ] 求值時類型不匹配 (如果 `asteval` 會產生此類錯誤)。
    - [ ] **安全性測試：**
        - [ ] 嘗試執行不應允許的Python代碼或訪問不應暴露的變量/模塊 (預期失敗或報錯)。
        - [ ] 確認隨機函數 (`rand_int`, `rand_float`) **不可用**。
    - [ ] **上下文隔離測試：** 多次調用 `evaluate` 使用不同的上下文變量，確認結果互不影響。

## 4. 文檔與示例

- [ ] 在 `rpg_system/formula_engine/evaluator.py` 中為 `FormulaEvaluator` 類及其方法添加清晰的文檔字符串 (docstrings)。
- [ ] (可選) 創建一個簡單的 Markdown 文檔或在 `RPG_09_Formula_Engine_Details.md` 中補充一些使用 `FormulaEvaluator` 的代碼示例。

---
完成所有上述任務後，請勾選並更新 `RPG_IMPLEMENTATION_PLAN.md` 中的相應條目。 