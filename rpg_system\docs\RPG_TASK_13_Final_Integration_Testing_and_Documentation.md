# RPG 任務 13：最終集成、測試與文檔

本文檔詳細列出了 RPG 系統開發的最後階段任務，包括端到端集成測試、整體文檔編寫與更新、代碼清理以及準備部署。

**參考設計文檔：** 所有之前的設計文檔和任務文檔。

**目標：** 確保 RPG 系統作為一個整體能夠穩定運行，易於理解和維護，並準備好集成到主 Bot 項目中。

## 1. 端到端 (E2E) 集成測試

手動或通過自動化腳本測試整個 RPG 系統的關鍵用戶流程。

- [ ] **PVE 戰鬥流程測試：**
    - [ ] **場景1：完整PVE樓層挑戰**
        *   使用 `/pve_start` 命令開始一個樓層。
        *   通過 `BattleView` 進行玩家操作 (選擇技能、選擇目標)。
        *   觀察 AI 是否正確行動。
        *   測試戰鬥日誌的生成和 Embed 更新是否符合預期 (`RPG_11_Battle_Presentation.md`)。
        *   測試戰鬥勝利：
            *   驗證 `rpg_user_progress` 表中進度是否正確更新 (樓層勝利次數、最高樓層解鎖)。
            *   驗證是否根據 `floors.json` 中的配置獲得了正確的獎勵 (首次通關/重複通關)。
            *   (如果實現了獎勵服務) 驗證獎勵是否實際發放到用戶賬戶。
        *   測試戰鬥失敗：
            *   驗證進度沒有不當更新。
            *   Embed 是否正確顯示失敗信息。
    - [ ] **場景2：特殊效果和被動觸發**
        *   選擇帶有明顯狀態效果 (如眩暈、中毒) 或被動技能 (如反擊、吸血) 的卡牌/怪物進行戰鬥。
        *   驗證效果是否按預期觸發和應用 (`PassiveTriggerHandler` 和 `EffectApplier`)。
        *   驗證狀態效果的持續時間和 Tick 效果。
    - [ ] **場景3：不同目標選擇邏輯**
        *   測試單體目標、群體目標、隨機目標等技能的目標選擇是否符合 `TargetSelector` 的設計。
    - [ ] **場景4：公式計算**
        *   使用包含複雜公式的技能/效果，驗證傷害/治療/數值調整是否由 `FormulaEvaluator` 正確計算。
- [ ] **卡牌與技能管理流程測試：**
    - [ ] **全局技能學習與升級：**
        *   使用 `/gskill_learn` 學習新技能。
        *   驗證 `gacha_user_learned_global_skills` 表數據。
        *   (如果實現了資源消耗) 驗證升級時是否消耗了正確的資源。
        *   使用 `/gskill_upgrade` 升級技能，驗證等級和XP變化。
        *   使用 `/gskill_list` 查看技能列表。
    - [ ] **卡牌技能裝備/卸下：**
        *   使用 `/card_equip_active` 和 `/card_equip_passive` 裝備技能。
        *   驗證 `gacha_user_collections` 表中 `equipped_active_skill_ids` 和 `equipped_common_passives` 是否正確更新。
        *   測試槽位限制、技能未學習等錯誤情況是否被正確處理。
        *   使用 `/card_unequip_active` 和 `/card_unequip_passive` 卸下技能。
        *   使用 `/card_skills_show` 查看卡牌技能。
- [ ] **配置加載與熱重載 (如果實現)：**
    - [ ] 驗證 `ConfigLoader` 是否能在啟動時正確加載所有 JSON 配置文件。
    - [ ] (如果實現了熱重載命令) 測試修改 JSON 文件後，不重啟 Bot 的情況下，配置是否能被更新並在遊戲中生效。
- [ ] **數據一致性檢查：**
    - [ ] 在多個操作後，檢查數據庫中相關聯的數據是否保持一致 (e.g., 玩家進度、卡牌技能、全局技能等級)。

## 2. 項目文檔

- [ ] **創建/更新 `rpg_system/README.md`：**
    - [ ] **系統概述：** 簡要介紹 RPG 系統的功能和目的。
    - [ ] **架構：** 引用 `RPG_01_System_Architecture.md` 或簡述其核心思想 (分層、配置驅動)。
    - [ ] **核心模塊：** 描述 `battle_system`, `config`, `services`, `repositories`, `cogs` 等主要目錄的功能。
    - [ ] **配置管理：**
        *   說明 JSON 配置文件位於 `rpg_system/config/data/`。
        *   簡要描述每個主要 JSON 文件的用途。
        *   說明 Pydantic 模型位於 `rpg_system/config/pydantic_models/` 並用於驗證。
        *   如何使用 `scripts/generation/generate_rpg_configs.py` 生成初始數據或更新數據結構。
    - [ ] **數據庫：**
        *   說明相關的數據庫表 (引用 `RPG_03_Database_Schema.md`)。
        *   如何運行數據庫遷移 (引用 `RPG_TASK_02_Database_Schema_and_Migrations.md`)。
    - [ ] **主要功能與 Discord 命令：**
        *   列出主要的 Discord 命令 (如 `/pve_start`, `/gskill_learn`) 及其功能。
    - [ ] **開發與貢獻指南 (可選)：**
        *   如何設置開發環境。
        *   代碼風格指南。
        *   如何添加新功能/配置。
- [ ] **更新主項目 `README.md`：**
    - [ ] 添加一個章節或鏈接，指向 RPG 系統的詳細文檔 (`rpg_system/README.md`)。
- [ ] **代碼文檔 (Docstrings)：**
    - [ ] 檢查所有主要的類、方法、函數是否都有清晰、準確的文檔字符串。
    - [ ] 確保類型提示完整且正確。
- [ ] **設計文檔歸檔：**
    - [ ] 確保所有 `docs/RPG/RPG_XX_*.md` 設計文檔都已最終審閱並存檔。
    - [ ] 在 `RPG_IMPLEMENTATION_PLAN.md` 中鏈接所有設計文檔和任務文檔，作為項目的完整記錄。

## 3. 代碼質量與清理

- [ ] **代碼審查 (Code Review)：**
    - [ ] (如果可能) 讓另一位開發者審查 RPG 系統的整體代碼，檢查邏輯、風格、潛在錯誤。
- [ ] **移除冗餘/調試代碼：**
    - [ ] 清理所有用於調試的 `print()` 語句、未使用的變量或臨時註釋掉的代碼塊。
- [ ] **配置文件檢查：**
    - [ ] 確保所有 JSON 配置文件中的初始數據是合理且平衡的 (至少對於初始版本)。
    - [ ] 移除不必要的示例數據。
- [ ] **依賴項檢查：**
    - [ ] 檢查 `requirements.txt` (或 `pyproject.toml`)，確保只包含項目實際需要的依賴。
- [ ] **Linter 和 Formatter：**
    - [ ] 運行如 `flake8` (或 `pylint`) 和 `black` (或 `autopep8`) 等工具，確保代碼風格一致且無明顯語法問題。

## 4. 部署與集成準備

- [ ] **確認 Cog 加載：** 確保 RPG 系統的 Cogs 在主 Bot 的 `setup_hook` 或類似機制中被正確加載。
- [ ] **環境變量：** 如果有特定於 RPG 系統的環境變量 (如數據庫連接細節，雖然通常是項目級別的)，確保它們已文檔化並在部署時配置正確。
- [ ] **性能初步評估：**
    - [ ] 對於戰鬥等核心操作，進行初步的性能評估，確保沒有明顯的延遲或資源瓶頸。
    - [ ] (如果發現問題，可能需要創建後續的優化任務)。

## 5. 更新 `RPG_IMPLEMENTATION_PLAN.md`

- [ ] **最終審查 `RPG_IMPLEMENTATION_PLAN.md`：**
    - [ ] 確保所有任務都已標記為完成。
    - [ ] 添加任何在集成測試階段發現並修復的小問題或調整的記錄。
    - [ ] 標記整個 RPG 系統開發項目為完成。

---
完成所有上述任務後，RPG 系統的第一個版本應準備就緒。 