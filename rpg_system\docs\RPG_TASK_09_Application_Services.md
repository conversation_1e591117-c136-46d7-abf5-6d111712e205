# RPG 任務 09：應用服務層

本文檔詳細列出了 RPG 系統應用服務層的實現任務。應用服務層負責協調領域模型和基礎設施服務，向上層 (如 Discord Cogs) 提供簡潔的接口來執行業務操作。

**參考設計文檔：**
*   `RPG_01_System_Architecture.md` (II. 層次結構 - Application Service Layer)
*   `RPG_05_Core_Runtime_Logic.md` (對服務職責的描述，如 Battle Preparation, Player Global Skill Proficiency Service, Player Card Skill Management Service)
*   `RPG_TASK_01_Project_Setup_and_Config_System.md` (已創建的服務文件存根)
*   `RPG_TASK_02_Database_Schema_and_Migrations.md` (數據庫表結構，服務可能需要與倉庫交互)
*   `RPG_TASK_04_Core_Domain_Models.md` (`Battle`, `Combatant` 等模型將被服務使用)
*   `RPG_TASK_05_Attribute_Calculator.md` (`AttributeCalculator` 將被使用)
*   `RPG_TASK_06_Effect_System_Handlers.md` (效果處理器可能被戰鬥協調服務間接使用)
*   `RPG_TASK_07_Event_System_and_Passive_Trigger_Handler.md` (事件處理器可能被戰鬥協調服務間接使用)
*   `RPG_TASK_08_Battle_System_Core_Logic.md` (`Battle` 類的核心邏輯將被 `BattleCoordinatorService` 封裝和調用)

**目標位置：** `rpg_system/services/` 目錄下的各服務文件。

## 1. `BattleCoordinatorService`

在 `rpg_system/services/battle_coordinator_service.py` 中實現。
此服務負責PVE戰鬥的準備、啟動、管理和結束。

- [ ] **定義 `BattleCoordinatorService` 類：**
    - [ ] **依賴注入 (`__init__`)：**
        - [ ] `config_loader: ConfigLoader`
        - [ ] `attribute_calculator: AttributeCalculator`
        - [ ] `effect_applier: EffectApplier`
        - [ ] `target_selector: TargetSelector`
        - [ ] `passive_trigger_handler: PassiveTriggerHandler`
        - [ ] `formula_evaluator: FormulaEvaluator`
        - [ ] `player_collection_repo: PlayerCollectionRPGRepository` (用於獲取玩家卡牌數據)
        - [ ] `user_progress_repo: UserProgressRPGRepository` (用於獲取和更新玩家PVE進度)
        - [ ] (未來可能需要 `MonsterRepository` 如果怪物數據不只在配置中)
    - [ ] **主要方法：**
        - [ ] `prepare_pve_battle(self, user_id: int, floor_id: str) -> Tuple[Optional[Battle], Optional[str]]`:
            *   **返回值：** `(Battle 實例, 錯誤信息)` 或 `(None, 錯誤信息)`
            *   **邏輯 (參考 `RPG_05` - 4. Battle Preparation)：**
                *   [ ] 獲取 `FloorConfig` (使用 `floor_id` 和 `config_loader`)。
                *   [ ] (如果需要) 檢查玩家進入樓層的條件 (e.g., `user_progress.current_floor_unlocked`)。
                *   [ ] 從 `FloorConfig` 中隨機選擇一個 `monster_group_id`。
                *   [ ] 獲取 `MonsterGroupConfig`。
                *   [ ] **創建玩家隊伍 (`player_team: List[Combatant]`)：**
                    *   [ ] 從 `user_progress_repo.get_user_progress(user_id)` 獲取 `current_team_formation` (玩家選擇出戰的 `gacha_user_collections.id` 列表)。
                    *   [ ] 對於每個 `collection_id`：
                        *   [ ] 從 `player_collection_repo.get_player_card_by_collection_id(collection_id)` 獲取卡牌數據。
                        *   [ ] `card_master_config = config_loader.get_card_config(player_card_db_data.card_id)`。
                        *   [ ] 創建 `SkillInstance` 對象 (普攻, 主動, 天賦, 被動) 基於 `player_card_db_data` (已裝備技能) 和 `card_master_config` (默認技能)。
                        *   [ ] `base_stats_from_config` 從 `card_master_config.base_stats`。
                        *   [ ] 創建 `Combatant` 實例 (玩家側)。
                        *   [ ] 調用 `attribute_calculator.calculate_attributes(...)` 計算完整屬性，並賦值給 `Combatant` 的 `max_hp`, `current_hp`, `max_mp`, `current_mp`, `calculated_rpg_stats`, `current_battle_stats`。
                *   [ ] **創建怪物隊伍 (`monster_team: List[Combatant]`)：**
                    *   [ ] 對於 `MonsterGroupConfig` 中的每個 `monster_in_group`：
                        *   [ ] `monster_config = config_loader.get_monster_config(monster_in_group.monster_id)`。
                        *   [ ] 創建 `SkillInstance` (普攻, 主動, 被動) 基於 `monster_config`。
                        *   [ ] `base_stats_from_config` 從 `monster_config`。
                        *   [ ] 創建 `Combatant` 實例 (怪物側)。
                        *   [ ] 調用 `attribute_calculator.calculate_attributes(...)` (對怪物而言，rpg_level 和 star_level 通常固定或為0)，賦值屬性。
                *   [ ] 創建 `Battle` 實例，傳入創建好的隊伍和所有必要的服務依賴 (`config_loader`, `effect_applier`, etc.)。
                *   [ ] 返回 `(battle_instance, None)` 或 `(None, error_message)`。
        - [ ] `start_battle(self, battle: Battle) -> Battle`:
            *   調用 `battle.start()`。
            *   返回 `battle`。
        - [ ] `process_player_action(self, battle: Battle, acting_player_combatant_id: str, skill_id: str, target_combatant_ids: List[str]) -> Tuple[Battle, Optional[str]]`:
            *   **返回值：** `(更新後的 Battle 實例, 錯誤信息)`
            *   [ ] 驗證 `acting_player_combatant_id` 是否為當前行動者且為玩家。
            *   [ ] `caster = battle.get_combatant_by_id(acting_player_combatant_id)`。
            *   [ ] 調用 `battle.process_action(caster, skill_id, target_combatant_ids)`。
            *   [ ] 返回 `(battle, None)` 或 `(battle, error_message)`。
        - [ ] `advance_battle_turn(self, battle: Battle) -> Battle`:
            *   **循環處理 AI 行動直到輪到玩家或戰鬥結束：**
                *   `current_actor = battle.next_turn_or_combatant()`
                *   `while current_actor and not current_actor.is_player_side and battle.battle_status == BattleStatus.IN_PROGRESS:`
                    *   `ai_skill_id, ai_target_ids = current_actor.get_ai_action(battle, self.config_loader)`
                    *   `battle.process_action(current_actor, ai_skill_id, ai_target_ids)`
                    *   `if battle.battle_status != BattleStatus.IN_PROGRESS: break`
                    *   `current_actor = battle.next_turn_or_combatant()`
            *   返回 `battle`。
        - [ ] `handle_pve_battle_completion(self, user_id: int, battle: Battle, floor_id: str) -> Dict[str, Any]`:
            *   **返回值：** 包含戰鬥結果、獎勵等信息的字典。
            *   [ ] 獲取 `FloorConfig`。
            *   [ ] 如果 `battle.battle_status == BattleStatus.PLAYER_WIN`:
                *   [ ] `user_progress = user_progress_repo.get_user_progress(user_id)`。
                *   [ ] `user_progress.current_floor_wins += 1`。
                *   [ ] `rewards_key_to_grant = None`
                *   [ ] `is_first_clear = False`
                *   [ ] `if user_progress.max_floor_cleared < int(floor_id):`
                    *   `rewards_key_to_grant = floor_config.first_clear_rewards_key`
                    *   `user_progress.max_floor_cleared = int(floor_id)`
                    *   `is_first_clear = True`
                *   `else:`
                    *   `rewards_key_to_grant = floor_config.repeatable_rewards_per_win_key`
                *   [ ] **處理通關 (`user_progress.current_floor_wins >= floor_config.wins_required_to_advance`):**
                    *   `user_progress.current_floor_unlocked = max(user_progress.current_floor_unlocked, int(floor_id) + 1)`
                    *   `user_progress.current_floor_wins = 0` (如果晉級後重置同層勝利次數)
                *   [ ] `user_progress_repo.update_user_progress(user_progress)`。
                *   [ ] 根據 `rewards_key_to_grant` 從 `config_loader.reward_packages` 獲取獎勵列表。
                *   [ ] (未來) 調用獎勵服務發放獎勵。
            *   [ ] (如果需要) 更新玩家 PVE 統計數據。
            *   [ ] 返回包含結果 (WIN/LOSS), 是否首次通關, 獲得的獎勵列表等的字典。

## 2. `PlayerCardManagementService`

在 `rpg_system/services/player_card_management_service.py` 中實現。
負責玩家卡牌的技能槽位管理、被動技能裝備/卸下。

- [ ] **定義 `PlayerCardManagementService` 類：**
    - [ ] **依賴注入 (`__init__`)：**
        - [ ] `config_loader: ConfigLoader`
        - [ ] `player_collection_repo: PlayerCollectionRPGRepository`
        - [ ] `global_skill_repo: GlobalSkillRepository` (用於檢查玩家是否已學習該技能)
    - [ ] **主要方法：**
        - [ ] `equip_active_skill(self, user_id: int, collection_id: int, active_skill_id: str, slot_index: int) -> Tuple[bool, str]`:
            *   **返回值：** `(成功/失敗, 消息)`
            *   [ ] 獲取 `PlayerCardDBData` from `player_collection_repo`。
            *   [ ] 獲取 `CardConfig` from `config_loader`。
            *   [ ] 驗證 `slot_index` 是否有效。
            *   [ ] 驗證玩家是否已學習 `active_skill_id` (從 `global_skill_repo`)。
            *   [ ] 更新 `player_card_db_data.equipped_active_skill_ids` 列表。
            *   [ ] `player_collection_repo.update_player_card_skills(player_card_db_data)`。
        - [ ] `unequip_active_skill(self, user_id: int, collection_id: int, slot_index: int) -> Tuple[bool, str]`:
            *   類似 `equip_active_skill`，將槽位置為 `None`。
        - [ ] `equip_passive_skill(self, user_id: int, collection_id: int, passive_skill_id: str, passive_skill_level: int, slot_key: str) -> Tuple[bool, str]`:
            *   **返回值：** `(成功/失敗, 消息)`
            *   [ ] 獲取 `PlayerCardDBData` 和 `CardConfig`。
            *   [ ] 驗證卡牌是否有足夠的被動槽 (`CardConfig.passive_skill_slots`，以及是否通過星級解鎖更多)。
            *   [ ] 驗證 `slot_key` 格式 (e.g., "slot_0", "slot_1").
            *   [ ] 驗證玩家是否已學習 `passive_skill_id` 且等級足夠。
            *   [ ] 檢查是否與天賦技能衝突 (如果設計允許替換天賦或有特定規則)。
            *   [ ] 更新 `player_card_db_data.equipped_common_passives` JSONB 對象 (e.g., `{"slot_0": {"skill_id": "passive_A", "level": 5}}`)。
            *   [ ] `player_collection_repo.update_player_card_passives(player_card_db_data)`。
        - [ ] `unequip_passive_skill(self, user_id: int, collection_id: int, slot_key: str) -> Tuple[bool, str]`:
            *   類似 `equip_passive_skill`，將槽位從 JSONB 中移除或置為 `null`。
        - [ ] `get_card_details_for_display(self, user_id: int, collection_id: int) -> Optional[Dict[str, Any]]`:
            *   獲取卡牌數據 (DB + Config)，計算其屬性，格式化後用於前端展示。

## 3. `PlayerSkillManagementService`

在 `rpg_system/services/player_skill_management_service.py` 中實現。
負責玩家全局技能的學習、升級、獲取進度等。

- [ ] **定義 `PlayerSkillManagementService` 類：**
    - [ ] **依賴注入 (`__init__`)：**
        - [ ] `config_loader: ConfigLoader`
        - [ ] `global_skill_repo: GlobalSkillRepository`
    - [ ] **主要方法：**
        - [ ] `learn_global_skill(self, user_id: int, skill_id: str, skill_type: Literal["ACTIVE", "PASSIVE"]) -> Tuple[bool, str]`:
            *   [ ] 檢查技能是否存在於配置中 (`active_skills.json` 或 `passive_skills.json`)。
            *   [ ] 檢查玩家是否已學習此技能。
            *   [ ] `global_skill_repo.add_learned_skill(...)`。
        - [ ] `upgrade_global_skill(self, user_id: int, skill_id: str, skill_type: Literal["ACTIVE", "PASSIVE"]) -> Tuple[bool, str]`:
            *   [ ] 獲取玩家已學習的技能數據 `LearnedGlobalSkillDBData`。
            *   [ ] 獲取技能配置 (`ActiveSkillConfig` 或 `PassiveSkillConfig`)。
            *   [ ] 檢查是否達到最大等級 (`max_level`)。
            *   [ ] 檢查是否有足夠的 XP (`skill_xp` vs `xp_to_next_level_config` from skill config)。
                *   `xp_to_next_level_config` 的解析和使用需要在此處實現。
            *   [ ] 如果可以升級，消耗 XP，增加 `skill_level`，重置 `skill_xp`。
            *   [ ] `global_skill_repo.update_learned_skill(...)`。
        - [ ] `add_xp_to_global_skill(self, user_id: int, skill_id: str, skill_type: Literal["ACTIVE", "PASSIVE"], xp_amount: int) -> Tuple[Optional[LearnedGlobalSkillDBData], str]`:
            *   增加 XP，但不處理升級，升級由 `upgrade_global_skill` 處理或在此處觸發升級檢查。
        - [ ] `get_player_learned_skills(self, user_id: int, skill_type: Optional[Literal["ACTIVE", "PASSIVE"]] = None) -> List[Dict[str, Any]]`:
            *   從 `global_skill_repo` 獲取玩家技能列表，並結合配置信息 (名稱, 描述, 當前等級效果) 返回。
        - [ ] `get_skill_xp_requirements(self, skill_config: Union[ActiveSkillConfig, PassiveSkillConfig], current_level: int) -> Optional[int]`:
            *   輔助方法，根據技能配置中的 `xp_to_next_level_config` 和當前等級計算升到下一級所需的XP。

## 4. 單元測試

- [ ] **為 `BattleCoordinatorService` 編寫單元/集成測試：**
    - [ ] Mock 依賴項 (Repositories, ConfigLoader, other Services)。
    - [ ] 測試 `prepare_pve_battle`：
        - [ ] 玩家隊伍和怪物隊伍的正確創建和屬性計算。
        - [ ] 無效 floor_id 或玩家數據不足的處理。
    - [ ] 測試 `start_battle` 的調用。
    - [ ] 測試 `process_player_action`：
        - [ ] 玩家行動的正確傳遞和 `Battle` 狀態更新。
    - [ ] 測試 `advance_battle_turn`：
        - [ ] AI 行動的循環和正確停止條件。
    - [ ] 測試 `handle_pve_battle_completion`：
        - [ ] 勝利/失敗後的進度更新和獎勵計算。
- [ ] **為 `PlayerCardManagementService` 編寫單元測試：**
    - [ ] Mock 依賴項。
    - [ ] 測試主動/被動技能的裝備和卸下：
        - [ ] 成功情況。
        - [ ] 槽位無效、技能未學習、槽位已滿等錯誤情況。
        - [ ] DB數據的正確更新。
- [ ] **為 `PlayerSkillManagementService` 編寫單元測試：**
    - [ ] Mock 依賴項。
    - [ ] _測試技能學習、升級、增加XP：
        - [ ] XP計算、等級上限、資源消耗的正確性。
    - [ ] 測試獲取玩家技能列表。

## 5. 文檔與類型提示

- [ ] 為所有服務類及其公共方法添加清晰的文檔字符串。
- [ ] 使用 Python 的類型提示，並通過 `TYPE_CHECKING` 塊處理循環依賴。

---
完成所有上述任務後，請勾選並更新 `RPG_IMPLEMENTATION_PLAN.md` 中的相應條目。 