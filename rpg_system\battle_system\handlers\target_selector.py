"""
目標選擇器 (TargetSelector)
根據技能定義和戰場情況選擇合適的目標
"""

from typing import List, Dict, Any, Optional, TYPE_CHECKING
import random
from utils.logger import logger

if TYPE_CHECKING:
    from ..models.combatant import Combatant
    from ..models.battle import Battle
    from ...formula_engine.evaluator import FormulaEvaluator
    from ...config.loader import ConfigLoader


class TargetSelector:
    """目標選擇器服務"""
    
    def __init__(self):
        """初始化目標選擇器"""
        pass
    
    def select_targets(
        self, 
        caster: 'Combatant', 
        target_logic_detail: Dict[str, Any], 
        battle_context: 'Battle', 
        formula_evaluator: 'FormulaEvaluator', 
        config_loader: 'ConfigLoader'
    ) -> List['Combatant']:
        """
        選擇目標
        
        Args:
            caster: 施法者
            target_logic_detail: 目標邏輯詳情（來自技能配置）
            battle_context: 戰鬥上下文
            formula_evaluator: 公式求值器
            config_loader: 配置加載器
            
        Returns:
            符合條件的目標列表
        """
        try:
            # 1. 確定基礎目標池
            potential_targets = self._get_base_target_pool(
                caster, target_logic_detail, battle_context
            )
            
            if not potential_targets:
                return []
            
            # 2. 應用目標條件過濾
            filtered_targets = self._apply_target_conditions(
                potential_targets, caster, target_logic_detail, 
                formula_evaluator, battle_context
            )
            
            if not filtered_targets:
                return []
            
            # 3. 應用排序邏輯
            sorted_targets = self._apply_sorting(
                filtered_targets, target_logic_detail
            )
            
            # 4. 選擇數量
            target_count = self._calculate_target_count(
                caster, target_logic_detail, formula_evaluator, 
                len(sorted_targets)
            )
            
            # 5. 應用選擇策略
            final_targets = self._apply_selection_strategy(
                sorted_targets, target_count, target_logic_detail, battle_context
            )
            
            return final_targets
            
        except Exception as e:
            logger.error(f"目標選擇錯誤: {e}")
            return []
    
    def _get_base_target_pool(
        self, 
        caster: 'Combatant', 
        target_logic_detail: Dict[str, Any], 
        battle_context: 'Battle'
    ) -> List['Combatant']:
        """
        獲取基礎目標池
        
        Args:
            caster: 施法者
            target_logic_detail: 目標邏輯詳情
            battle_context: 戰鬥上下文
            
        Returns:
            基礎目標列表
        """
        base_pool = target_logic_detail.get('base_pool', 'ENEMIES')
        
        if base_pool == "ENEMIES":
            return battle_context.get_all_alive_enemies_of(caster)
        elif base_pool == "ALLIES":
            return battle_context.get_all_alive_allies_of(caster)
        elif base_pool == "SELF":
            return [caster] if caster.is_alive() else []
        elif base_pool == "ALL_ALIVE":
            return battle_context.get_all_alive_combatants()
        else:
            logger.warning(f"未知的基礎目標池類型: {base_pool}")
            return []
    
    def _apply_target_conditions(
        self,
        potential_targets: List['Combatant'],
        caster: 'Combatant',
        target_logic_detail: Dict[str, Any],
        formula_evaluator: 'FormulaEvaluator',
        battle_context: 'Battle'
    ) -> List['Combatant']:
        """
        應用目標條件過濾
        
        Args:
            potential_targets: 潛在目標列表
            caster: 施法者
            target_logic_detail: 目標邏輯詳情
            formula_evaluator: 公式求值器
            battle_context: 戰鬥上下文
            
        Returns:
            過濾後的目標列表
        """
        conditions = target_logic_detail.get('conditions', [])
        if not conditions:
            return potential_targets
        
        filtered_targets = []
        
        for target_candidate in potential_targets:
            passes_all_conditions = True
            
            for condition in conditions:
                # 準備公式上下文
                context_vars = self._prepare_condition_context(
                    caster, target_candidate, battle_context
                )
                
                # 評估條件公式
                condition_formula = condition.get('formula', '1')  # 默認為真
                condition_met = formula_evaluator.evaluate(condition_formula, context_vars)
                
                if not condition_met:
                    passes_all_conditions = False
                    break
            
            if passes_all_conditions:
                filtered_targets.append(target_candidate)
        
        return filtered_targets
    
    def _prepare_condition_context(
        self,
        caster: 'Combatant',
        target: 'Combatant',
        battle_context: 'Battle'
    ) -> Dict[str, Any]:
        """
        準備條件評估的上下文變量
        
        Args:
            caster: 施法者
            target: 目標
            battle_context: 戰鬥上下文
            
        Returns:
            上下文變量字典
        """
        return {
            # 施法者相關
            'caster_stat_hp': caster.current_hp,
            'caster_stat_max_hp': caster.max_hp,
            'caster_stat_mp': caster.current_mp,
            'caster_stat_max_mp': caster.max_mp,
            'caster_stat_patk': caster.current_stats.get('patk', 0),
            'caster_stat_pdef': caster.current_stats.get('pdef', 0),
            'caster_stat_matk': caster.current_stats.get('matk', 0),
            'caster_stat_mdef': caster.current_stats.get('mdef', 0),
            'caster_stat_spd': caster.current_stats.get('spd', 0),
            'caster_current_hp_percent': caster.current_hp / max(caster.max_hp, 1),
            'caster_missing_hp_percent': 1 - (caster.current_hp / max(caster.max_hp, 1)),
            
            # 目標相關
            'target_stat_hp': target.current_hp,
            'target_stat_max_hp': target.max_hp,
            'target_stat_mp': target.current_mp,
            'target_stat_max_mp': target.max_mp,
            'target_stat_patk': target.current_stats.get('patk', 0),
            'target_stat_pdef': target.current_stats.get('pdef', 0),
            'target_stat_matk': target.current_stats.get('matk', 0),
            'target_stat_mdef': target.current_stats.get('mdef', 0),
            'target_stat_spd': target.current_stats.get('spd', 0),
            'target_current_hp_percent': target.current_hp / max(target.max_hp, 1),
            'target_missing_hp_percent': 1 - (target.current_hp / max(target.max_hp, 1)),
            'target_is_boss': 1 if getattr(target, 'is_boss', False) else 0,
            
            # 戰鬥相關
            'current_turn': battle_context.current_turn,
        }
    
    def _apply_sorting(
        self,
        targets: List['Combatant'],
        target_logic_detail: Dict[str, Any]
    ) -> List['Combatant']:
        """
        應用排序邏輯
        
        Args:
            targets: 目標列表
            target_logic_detail: 目標邏輯詳情
            
        Returns:
            排序後的目標列表
        """
        sort_by = target_logic_detail.get('sort_by')
        if not sort_by or not targets:
            return targets
        
        sort_order = target_logic_detail.get('sort_order', 'ASC')
        reverse_sort = (sort_order == 'DESC')
        
        try:
            # 根據排序字段獲取排序鍵
            def get_sort_key(combatant: 'Combatant') -> float:
                if hasattr(combatant, 'get_stat'):
                    return combatant.get_stat(sort_by)
                elif sort_by in ['current_hp', 'max_hp', 'current_mp', 'max_mp']:
                    return getattr(combatant, sort_by, 0)
                elif sort_by in combatant.current_stats:
                    return combatant.current_stats[sort_by]
                else:
                    return 0
            
            return sorted(targets, key=get_sort_key, reverse=reverse_sort)
            
        except Exception as e:
            logger.warning(f"排序失敗: {e}，返回原始順序")
            return targets
    
    def _calculate_target_count(
        self,
        caster: 'Combatant',
        target_logic_detail: Dict[str, Any],
        formula_evaluator: 'FormulaEvaluator',
        available_count: int
    ) -> int:
        """
        計算要選擇的目標數量
        
        Args:
            caster: 施法者
            target_logic_detail: 目標邏輯詳情
            formula_evaluator: 公式求值器
            available_count: 可用目標數量
            
        Returns:
            要選擇的目標數量
        """
        count_logic = target_logic_detail.get('count_logic', 'SINGLE')
        
        if count_logic == 'ALL':
            return available_count
        elif count_logic == 'FORMULA':
            count_formula = target_logic_detail.get('count_formula', '1')
            context_vars = {
                'caster_stat_patk': caster.current_stats.get('patk', 0),
                'caster_stat_matk': caster.current_stats.get('matk', 0),
                'available_targets': available_count,
            }
            calculated_count = formula_evaluator.evaluate(count_formula, context_vars)
            return max(0, int(calculated_count))
        else:
            # 默認為單個目標
            return 1
    
    def _apply_selection_strategy(
        self,
        targets: List['Combatant'],
        target_count: int,
        target_logic_detail: Dict[str, Any],
        battle_context: 'Battle'
    ) -> List['Combatant']:
        """
        應用選擇策略
        
        Args:
            targets: 排序後的目標列表
            target_count: 要選擇的目標數量
            target_logic_detail: 目標邏輯詳情
            battle_context: 戰鬥上下文
            
        Returns:
            最終選擇的目標列表
        """
        if not targets or target_count <= 0:
            return []
        
        # 確保不超過可用目標數量
        target_count = min(target_count, len(targets))
        
        selection_strategy = target_logic_detail.get('selection_strategy', 'FIRST_N')
        
        if selection_strategy == 'FIRST_N':
            return targets[:target_count]
        elif selection_strategy == 'LAST_N':
            return targets[-target_count:]
        elif selection_strategy == 'RANDOM_N':
            # 使用戰鬥上下文的隨機數生成器確保可復現性
            if hasattr(battle_context, '_rng'):
                return battle_context._rng.sample(targets, target_count)
            else:
                # 降級到標準隨機
                return random.sample(targets, target_count)
        else:
            # 默認為 FIRST_N
            logger.warning(f"未知的選擇策略: {selection_strategy}，使用 FIRST_N")
            return targets[:target_count]
