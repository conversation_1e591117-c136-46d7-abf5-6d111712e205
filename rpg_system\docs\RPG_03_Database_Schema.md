**III. 數據庫表結構**

### 1. `gacha_master_cards`

*   **職責：** 存儲所有可抽取的卡牌的基礎靜態定義。`cards.json` 中的配置將直接使用本表的 `card_id` 作為主鍵進行關聯。
*   **現有核心字段 (部分，RPG相關或用於生成 `cards.json`)：**
    *   `card_id` (主鍵, 整數): 卡牌的唯一標識。
    *   `name` (字符串)
    *   `rarity` (整數, 1-7): 卡牌本身的稀有度 (現有字段)。此稀有度將影響 `cards.json` 中對應卡牌的基礎屬性、成長潛力以及可獲得的天賦/技能稀有度範疇。
    *   `pool_type` (字符串, 例如 'main'): 卡池類型 (現有字段)，也可能影響 `cards.json` 的生成規則。
    *   ... (其他現有展示性或基礎屬性)

### 2. `gacha_user_learned_global_skills`
   *   **職責：** 存儲用戶對各個通用主動技能和通用被動技能的「全局熟練度等級」。當卡牌裝備了某個 `skill_id` 的技能時，其在戰鬥中生效的等級將由此處定義的全局熟練度等級決定。
   *   **主鍵：** (`user_id`, `skill_id`, `skill_type`)
   *   **字段：**
       *   `user_id` (主鍵部分, 外鍵, 指向用戶表)
       *   `skill_id` (主鍵部分, 字符串, 指向 `active_skills.json` 或 `passive_skills.json` 的鍵)
       *   `skill_type` (主鍵部分, 枚舉: "ACTIVE", "PASSIVE")
       *   `skill_level` (整數, 默認1): 玩家對該 `skill_id` 的全局熟練度等級。
       *   `skill_xp` (整數, 默認0): 該全局熟練度等級當前獲得的經驗值。
       *   `unlocked_at` (時間戳, 默認 CURRENT_TIMESTAMP): 玩家首次記錄該技能熟練度的時間（例如，通過教學、初始給予，或首次獲得提升熟練度的途徑時）。
3.  **`gacha_user_collections`**:
    *   `id` (PK), `user_id`, `card_id` (FK), `quantity`, `star_level` (0-35), `current_rpg_level`, `current_rpg_xp`. (移除了 next_rpg_level_xp_required)
    *   `is_favorite` (布爾值, 現有字段): 標記卡牌是否為用戶的最愛，用於在批量獻祭等操作中進行保護。
    *   `equipped_common_passives` (JSONB, e.g., `{"slot_0": "passive_A_id", "slot_1": "passive_B_id", "slot_2": null}`). 存儲已裝備的通用被動技能的 `skill_id`。鍵為槽位索引 (0 至 `cards.json` 中定義的 `passive_skill_slots - 1`)，值為 `passive_skills.json` 中的 `skill_id` 或 `null`。技能生效等級由 `gacha_user_learned_global_skills` 中對應的全局熟練度等級決定。
    *   **新增/確認RPG相關字段：**
        *   `rpg_level` (整數, 默認1): 卡牌的RPG等級，通過戰鬥經驗提升。
        *   `rpg_xp` (整數, 默認0): 卡牌當前RPG等級的經驗值。
        *   `star_level` (整數, 默認0, 範圍 0-35): 卡牌的「培養星級」(現有字段)。在RPG系統中，此字段直接決定卡牌的天賦效果檔位 (對應 `innate_passive_skills.json` 的 `effects_by_star_level`) 和 `star_level_effects.json` 的加成。
        *   `equipped_active_skill_ids` (JSON數組 of 字符串, e.g., `["skill_X_id", "skill_Y_id", null]`, 存儲 `active_skills.json` 中的 `skill_id` 或 `null`): 裝備的通用主動技能的 `skill_id`。數組的順序代表玩家設定的技能釋放優先級。數組長度應與卡牌可裝備的主動技能槽數量一致。技能生效等級由 `gacha_user_learned_global_skills` 中對應的全局熟練度等級決定。
4.  **`rpg_user_progress`**: `user_id` (PK), `current_floor_unlocked`, `current_floor_wins`, `max_floor_cleared`, `current_team_formation` (JSONB, 卡牌實例ID列表). 