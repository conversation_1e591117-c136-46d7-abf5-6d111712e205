"""
被動觸發處理器 (PassiveTriggerHandler)
處理被動技能的觸發條件檢查和效果應用
"""

from typing import List, Dict, Any, Optional, TYPE_CHECKING
from utils.logger import logger

if TYPE_CHECKING:
    from ..models.combatant import Combatant
    from ..models.battle import Battle
    from ..models.skill_instance import SkillInstance
    from ...formula_engine.evaluator import FormulaEvaluator
    from ...config.loader import ConfigLoader
    from .effect_applier import EffectApplier


class PassiveTriggerHandler:
    """被動觸發處理器服務"""
    
    def __init__(
        self,
        formula_evaluator: 'FormulaEvaluator',
        effect_applier: 'EffectApplier',
        config_loader: 'ConfigLoader'
    ):
        """
        初始化被動觸發處理器
        
        Args:
            formula_evaluator: 公式求值器
            effect_applier: 效果應用器
            config_loader: 配置加載器
        """
        self.formula_evaluator = formula_evaluator
        self.effect_applier = effect_applier
        self.config_loader = config_loader
    
    def check_and_apply_passives(
        self,
        event_type: str,
        event_data: Dict[str, Any],
        potential_passive_owners: List['Combatant'],
        battle_context: 'Battle'
    ) -> List[Dict[str, Any]]:
        """
        檢查並應用被動技能
        
        Args:
            event_type: 事件類型（如 ON_DEAL_DAMAGE, ON_TAKE_DAMAGE 等）
            event_data: 事件數據
            potential_passive_owners: 可能擁有被動技能的戰鬥單位列表
            battle_context: 戰鬥上下文
            
        Returns:
            被動技能觸發結果列表
        """
        results = []
        
        for combatant in potential_passive_owners:
            if not combatant.is_alive():
                continue
            
            # 檢查天賦被動技能
            if combatant.innate_passive:
                passive_results = self._check_passive_skill(
                    combatant, combatant.innate_passive, event_type, 
                    event_data, battle_context, is_innate=True
                )
                results.extend(passive_results)
            
            # 檢查通用被動技能
            for passive_skill in combatant.common_passives:
                passive_results = self._check_passive_skill(
                    combatant, passive_skill, event_type, 
                    event_data, battle_context, is_innate=False
                )
                results.extend(passive_results)
        
        return results
    
    def _check_passive_skill(
        self,
        owner: 'Combatant',
        passive_skill: 'SkillInstance',
        event_type: str,
        event_data: Dict[str, Any],
        battle_context: 'Battle',
        is_innate: bool = False
    ) -> List[Dict[str, Any]]:
        """
        檢查單個被動技能是否觸發
        
        Args:
            owner: 被動技能擁有者
            passive_skill: 被動技能實例
            event_type: 事件類型
            event_data: 事件數據
            battle_context: 戰鬥上下文
            is_innate: 是否為天賦技能
            
        Returns:
            被動技能觸發結果列表
        """
        try:
            # 獲取被動技能定義
            all_configs = self.config_loader.get_all_configs()
            skill_definition = passive_skill.get_definition(all_configs)
            
            if not skill_definition:
                return []
            
            # 獲取被動效果塊列表
            if is_innate:
                # 天賦技能根據星級獲取效果
                star_level = owner.star_level
                effects_by_star = skill_definition.get('effects_by_star_level', {})
                passive_effect_blocks = effects_by_star.get(str(star_level), {}).get('passive_effect_blocks', [])
            else:
                # 通用被動技能根據等級獲取效果
                skill_level = passive_skill.current_level
                effects_by_level = skill_definition.get('effects_by_level', {})
                passive_effect_blocks = effects_by_level.get(str(skill_level), {}).get('passive_effect_blocks', [])
            
            results = []
            
            # 檢查每個被動效果塊
            for effect_block in passive_effect_blocks:
                block_results = self._process_passive_effect_block(
                    owner, effect_block, event_type, event_data, 
                    battle_context, passive_skill, is_innate
                )
                results.extend(block_results)
            
            return results
            
        except Exception as e:
            logger.error(f"檢查被動技能錯誤: {e}")
            return []
    
    def _process_passive_effect_block(
        self,
        owner: 'Combatant',
        effect_block: Dict[str, Any],
        event_type: str,
        event_data: Dict[str, Any],
        battle_context: 'Battle',
        passive_skill: 'SkillInstance',
        is_innate: bool
    ) -> List[Dict[str, Any]]:
        """
        處理被動效果塊
        
        Args:
            owner: 被動技能擁有者
            effect_block: 被動效果塊定義
            event_type: 事件類型
            event_data: 事件數據
            battle_context: 戰鬥上下文
            passive_skill: 被動技能實例
            is_innate: 是否為天賦技能
            
        Returns:
            效果應用結果列表
        """
        try:
            # 檢查觸發條件
            trigger_condition = effect_block.get('trigger_condition', {})
            if not self._check_trigger_condition(
                trigger_condition, event_type, event_data, owner, battle_context, is_innate
            ):
                return []
            
            # 計算觸發機率
            if not self._check_trigger_chance(
                effect_block, owner, battle_context, is_innate
            ):
                return []
            
            # 確定目標
            targets = self._determine_passive_targets(
                owner, effect_block, event_data, battle_context
            )
            
            if not targets:
                return []
            
            # 應用效果
            effect_definitions = effect_block.get('effect_definitions', [])
            if not effect_definitions:
                return []
            
            # 準備自定義變量
            custom_vars = self._prepare_passive_context_vars(
                owner, event_data, passive_skill, is_innate
            )
            
            # 應用效果定義
            return self.effect_applier.apply_effect_definitions(
                caster=owner,
                initial_targets=targets,
                effect_definitions=effect_definitions,
                battle_context=battle_context,
                source_skill_tags=effect_block.get('tags', []),
                source_skill_instance=passive_skill,
                custom_vars_from_source=custom_vars
            )
            
        except Exception as e:
            logger.error(f"處理被動效果塊錯誤: {e}")
            return []
    
    def _check_trigger_condition(
        self,
        trigger_condition: Dict[str, Any],
        event_type: str,
        event_data: Dict[str, Any],
        owner: 'Combatant',
        battle_context: 'Battle',
        is_innate: bool
    ) -> bool:
        """
        檢查觸發條件
        
        Args:
            trigger_condition: 觸發條件定義
            event_type: 事件類型
            event_data: 事件數據
            owner: 被動技能擁有者
            battle_context: 戰鬥上下文
            is_innate: 是否為天賦技能
            
        Returns:
            是否滿足觸發條件
        """
        # 檢查事件類型匹配
        required_event_type = trigger_condition.get('event_type')
        if required_event_type and required_event_type != event_type:
            return False
        
        # 檢查額外條件
        additional_conditions = trigger_condition.get('additional_conditions', [])
        if not additional_conditions:
            return True
        
        # 準備條件評估上下文
        context_vars = self._prepare_condition_context(
            owner, event_data, battle_context, is_innate
        )
        
        # 評估所有額外條件
        for condition in additional_conditions:
            condition_formula = condition.get('formula', '1')
            if not self.formula_evaluator.evaluate(condition_formula, context_vars):
                return False
        
        return True
    
    def _check_trigger_chance(
        self,
        effect_block: Dict[str, Any],
        owner: 'Combatant',
        battle_context: 'Battle',
        is_innate: bool
    ) -> bool:
        """
        檢查觸發機率
        
        Args:
            effect_block: 被動效果塊定義
            owner: 被動技能擁有者
            battle_context: 戰鬥上下文
            is_innate: 是否為天賦技能
            
        Returns:
            是否觸發成功
        """
        chance_formula = effect_block.get('chance_formula', '1.0')
        
        # 準備機率計算上下文
        context_vars = {
            'caster_star_level': owner.star_level if is_innate else 0,
            'skill_level': 0 if is_innate else getattr(effect_block, 'skill_level', 1),
            'caster_stat_patk': owner.current_stats.get('patk', 0),
            'caster_stat_matk': owner.current_stats.get('matk', 0),
            'caster_stat_crit_rate': owner.current_stats.get('crit_rate', 0),
            'caster_current_hp_percent': owner.current_hp / max(owner.max_hp, 1),
        }
        
        # 計算觸發機率
        trigger_chance = self.formula_evaluator.evaluate(chance_formula, context_vars)
        trigger_chance = max(0.0, min(1.0, trigger_chance))  # 限制在0-1之間
        
        # 隨機判定
        if hasattr(battle_context, '_rng'):
            roll = battle_context._rng.random()
        else:
            import random
            roll = random.random()
        
        return roll < trigger_chance
    
    def _determine_passive_targets(
        self,
        owner: 'Combatant',
        effect_block: Dict[str, Any],
        event_data: Dict[str, Any],
        battle_context: 'Battle'
    ) -> List['Combatant']:
        """
        確定被動技能的目標
        
        Args:
            owner: 被動技能擁有者
            effect_block: 被動效果塊定義
            event_data: 事件數據
            battle_context: 戰鬥上下文
            
        Returns:
            目標列表
        """
        target_override = effect_block.get('target_override')
        
        if target_override:
            # 使用目標選擇器
            from .target_selector import TargetSelector
            target_selector = TargetSelector()
            return target_selector.select_targets(
                owner, target_override, battle_context,
                self.formula_evaluator, self.config_loader
            )
        else:
            # 默認目標為擁有者自己
            return [owner]
    
    def _prepare_passive_context_vars(
        self,
        owner: 'Combatant',
        event_data: Dict[str, Any],
        passive_skill: 'SkillInstance',
        is_innate: bool
    ) -> Dict[str, Any]:
        """
        準備被動技能的上下文變量
        
        Args:
            owner: 被動技能擁有者
            event_data: 事件數據
            passive_skill: 被動技能實例
            is_innate: 是否為天賦技能
            
        Returns:
            上下文變量字典
        """
        context_vars = {
            'passive_owner_id': owner.instance_id,
            'is_innate_passive': 1 if is_innate else 0,
        }
        
        # 添加技能相關變量
        if is_innate:
            context_vars['star_level'] = owner.star_level
        else:
            context_vars['skill_level'] = passive_skill.current_level
        
        # 添加事件數據
        context_vars.update(event_data)
        
        return context_vars
    
    def _prepare_condition_context(
        self,
        owner: 'Combatant',
        event_data: Dict[str, Any],
        battle_context: 'Battle',
        is_innate: bool
    ) -> Dict[str, Any]:
        """
        準備條件評估的上下文
        
        Args:
            owner: 被動技能擁有者
            event_data: 事件數據
            battle_context: 戰鬥上下文
            is_innate: 是否為天賦技能
            
        Returns:
            條件評估上下文
        """
        context = {
            # 擁有者相關
            'owner_stat_hp': owner.current_hp,
            'owner_stat_max_hp': owner.max_hp,
            'owner_current_hp_percent': owner.current_hp / max(owner.max_hp, 1),
            'owner_missing_hp_percent': 1 - (owner.current_hp / max(owner.max_hp, 1)),
            'owner_stat_patk': owner.current_stats.get('patk', 0),
            'owner_stat_matk': owner.current_stats.get('matk', 0),
            
            # 戰鬥相關
            'current_turn': battle_context.current_turn,
            
            # 被動技能相關
            'is_innate': 1 if is_innate else 0,
            'star_level': owner.star_level if is_innate else 0,
        }
        
        # 添加事件數據
        context.update(event_data)
        
        return context
