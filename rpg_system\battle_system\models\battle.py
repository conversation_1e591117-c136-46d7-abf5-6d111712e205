"""
戰鬥實例領域模型
"""

from dataclasses import dataclass, field
from typing import List, Optional, Dict, Any
from enum import Enum
import uuid

from .combatant import Combatant
from .battle_log import BattleLogEntry


class BattleStatus(Enum):
    """戰鬥狀態枚舉"""
    PENDING = "PENDING"
    IN_PROGRESS = "IN_PROGRESS"
    PLAYER_WIN = "PLAYER_WIN"
    MONSTER_WIN = "MONSTER_WIN"
    DRAW = "DRAW"


@dataclass
class Battle:
    """戰鬥實例領域模型"""
    
    # 基本屬性
    battle_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    player_team: List[Combatant] = field(default_factory=list)
    monster_team: List[Combatant] = field(default_factory=list)
    current_turn: int = 0
    battle_log: List[BattleLogEntry] = field(default_factory=list)
    battle_status: BattleStatus = BattleStatus.PENDING
    rng_seed: Optional[int] = None
    combatant_queue: List[str] = field(default_factory=list)  # Combatant instance_ids
    
    # 戰鬥配置
    max_turns: int = 100  # 最大回合數，防止無限戰鬥
    
    def start(self) -> None:
        """
        初始化戰鬥
        計算所有Combatant的初始屬性，決定先手並生成初始行動順序隊列
        """
        if self.battle_status != BattleStatus.PENDING:
            raise ValueError("戰鬥已經開始或結束")
        
        # 初始化所有戰鬥單位的屬性
        all_combatants = self.player_team + self.monster_team
        for combatant in all_combatants:
            # 這裡會調用 combatant.calculate_final_stats()
            # 具體實現在 Combatant 類中
            pass
        
        # 生成初始行動順序隊列（按速度排序）
        self._generate_action_queue()
        
        # 設置戰鬥狀態
        self.battle_status = BattleStatus.IN_PROGRESS
        self.current_turn = 1
        
        # 記錄戰鬥開始日誌
        self.add_log_entry("戰鬥開始", "ENVIRONMENT", None, None, {})
    
    def next_turn(self) -> None:
        """
        推進到下一回合或下一行動單位
        處理回合開始/結束邏輯
        """
        if self.battle_status != BattleStatus.IN_PROGRESS:
            return
        
        # 移除當前行動單位
        if self.combatant_queue:
            self.combatant_queue.pop(0)
        
        # 如果隊列為空，進入下一回合
        if not self.combatant_queue:
            self.current_turn += 1
            
            # 檢查是否達到最大回合數
            if self.current_turn > self.max_turns:
                self.battle_status = BattleStatus.DRAW
                self.add_log_entry("戰鬥因達到最大回合數而結束", "ENVIRONMENT", None, None, {})
                return
            
            # 重新生成行動隊列
            self._generate_action_queue()
    
    def get_acting_combatant(self) -> Optional[Combatant]:
        """
        從行動隊列中獲取當前行動的單位
        
        Returns:
            當前行動的Combatant，如果隊列為空則返回None
        """
        if not self.combatant_queue:
            return None
        
        acting_id = self.combatant_queue[0]
        
        # 在所有戰鬥單位中查找
        all_combatants = self.player_team + self.monster_team
        for combatant in all_combatants:
            if combatant.instance_id == acting_id and combatant.is_alive():
                return combatant
        
        # 如果找不到或已死亡，移除並嘗試下一個
        self.combatant_queue.pop(0)
        return self.get_acting_combatant()
    
    def process_action(self, caster: Combatant, skill_id: str, target_ids: List[str]) -> None:
        """
        核心方法，處理一個單位的行動
        
        Args:
            caster: 施法者
            skill_id: 技能ID
            target_ids: 目標ID列表
        """
        # 這個方法的具體實現會在後續完成
        # 需要調用 EffectApplier 等處理器
        pass
    
    def check_win_condition(self) -> None:
        """檢查戰鬥是否結束"""
        if self.battle_status != BattleStatus.IN_PROGRESS:
            return
        
        # 檢查玩家隊伍是否全部死亡
        player_alive = any(combatant.is_alive() for combatant in self.player_team)
        monster_alive = any(combatant.is_alive() for combatant in self.monster_team)
        
        if not player_alive and not monster_alive:
            self.battle_status = BattleStatus.DRAW
            self.add_log_entry("雙方同歸於盡", "ENVIRONMENT", None, None, {})
        elif not player_alive:
            self.battle_status = BattleStatus.MONSTER_WIN
            self.add_log_entry("怪物獲勝", "ENVIRONMENT", None, None, {})
        elif not monster_alive:
            self.battle_status = BattleStatus.PLAYER_WIN
            self.add_log_entry("玩家獲勝", "ENVIRONMENT", None, None, {})
    
    def add_log_entry(self, message: str, action_type: str, actor_id: Optional[str], 
                     target_ids: Optional[List[str]], details: Dict[str, Any]) -> None:
        """
        記錄戰鬥日誌
        
        Args:
            message: 日誌消息
            action_type: 行動類型
            actor_id: 行動者ID
            target_ids: 目標ID列表
            details: 詳細信息
        """
        log_entry = BattleLogEntry(
            turn_number=self.current_turn,
            action_type=action_type,
            actor_id=actor_id,
            target_ids=target_ids or [],
            message=message,
            details=details
        )
        self.battle_log.append(log_entry)
    
    def _generate_action_queue(self) -> None:
        """生成行動順序隊列（按速度排序）"""
        all_combatants = self.player_team + self.monster_team
        alive_combatants = [c for c in all_combatants if c.is_alive()]
        
        # 按速度排序（速度高的先行動）
        alive_combatants.sort(key=lambda c: c.current_stats.get('spd', 0), reverse=True)
        
        # 生成隊列
        self.combatant_queue = [c.instance_id for c in alive_combatants]
    
    def get_combatant_by_id(self, instance_id: str) -> Optional[Combatant]:
        """
        根據ID獲取戰鬥單位
        
        Args:
            instance_id: 戰鬥單位實例ID
            
        Returns:
            對應的Combatant，如果找不到則返回None
        """
        all_combatants = self.player_team + self.monster_team
        for combatant in all_combatants:
            if combatant.instance_id == instance_id:
                return combatant
        return None

    def get_all_alive_enemies_of(self, combatant: Combatant) -> List[Combatant]:
        """
        獲取指定戰鬥單位的所有存活敵人

        Args:
            combatant: 指定的戰鬥單位

        Returns:
            敵方存活戰鬥單位列表
        """
        if combatant.is_player_side:
            # 玩家方的敵人是怪物方
            return [c for c in self.monster_team if c.is_alive()]
        else:
            # 怪物方的敵人是玩家方
            return [c for c in self.player_team if c.is_alive()]

    def get_all_alive_allies_of(self, combatant: Combatant) -> List[Combatant]:
        """
        獲取指定戰鬥單位的所有存活盟友（包括自己）

        Args:
            combatant: 指定的戰鬥單位

        Returns:
            己方存活戰鬥單位列表
        """
        if combatant.is_player_side:
            # 玩家方的盟友是玩家方
            return [c for c in self.player_team if c.is_alive()]
        else:
            # 怪物方的盟友是怪物方
            return [c for c in self.monster_team if c.is_alive()]

    def get_all_alive_combatants(self) -> List[Combatant]:
        """
        獲取所有存活的戰鬥單位

        Returns:
            所有存活戰鬥單位列表
        """
        all_combatants = self.player_team + self.monster_team
        return [c for c in all_combatants if c.is_alive()]
