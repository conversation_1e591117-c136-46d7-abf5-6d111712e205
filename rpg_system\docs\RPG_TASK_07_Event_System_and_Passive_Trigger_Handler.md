# RPG 任務 07：事件系統與被動觸發處理器

本文檔詳細列出了 RPG 系統中事件系統和 `PassiveTriggerHandler` 的實現任務。`PassiveTriggerHandler` 負責監聽戰鬥中發生的事件，並根據被動技能的觸發條件來激活相應的被動效果。

**參考設計文檔：**
*   `RPG_07_PassiveTriggerHandler.md` (核心設計)
*   `RPG_10_Event_System_Details.md` (標準事件類型及其數據結構)
*   `RPG_02_Configuration_Files.md` (`innate_passive_skills.json` 和 `passive_skills.json` 中的 `TriggerCondition` 和 `effects_by_level`/`effects_by_star_level`)
*   `RPG_04_Domain_Model_Battle_System.md` (`EffectApplier` 會被 `PassiveTriggerHandler` 用於應用被動技能的效果)

**目標位置：**
*   事件定義：可能在 `rpg_system/battle_system/events.py` (或 `constants.py`)
*   `PassiveTriggerHandler`: `rpg_system/battle_system/handlers/passive_trigger_handler.py`

## 1. 事件類型定義 (Event Types & Data Structures)

根據 `RPG_10_Event_System_Details.md`，定義標準事件類型和它們關聯的數據結構。
這些可以是在 `rpg_system/battle_system/events.py` 中定義的字符串常量和 TypedDicts/Pydantic 模型。

- [ ] **創建 `events.py` 文件。**
- [ ] **定義事件類型常量：**
    - [ ] `EVENT_ON_BATTLE_START = "ON_BATTLE_START"`
    - [ ] `EVENT_ON_BATTLE_END = "ON_BATTLE_END"`
    - [ ] `EVENT_ON_TURN_START = "ON_TURN_START"`
    - [ ] `EVENT_ON_TURN_END = "ON_TURN_END"`
    - [ ] `EVENT_ON_BEFORE_SKILL_USE = "ON_BEFORE_SKILL_USE"`
    - [ ] `EVENT_ON_AFTER_SKILL_USE = "ON_AFTER_SKILL_USE"`
    - [ ] `EVENT_ON_DEAL_DAMAGE = "ON_DEAL_DAMAGE"`
    - [ ] `EVENT_ON_TAKE_DAMAGE = "ON_TAKE_DAMAGE"`
    - [ ] `EVENT_ON_HEAL_DEALT = "ON_HEAL_DEALT"`
    - [ ] `EVENT_ON_HEAL_RECEIVED = "ON_HEAL_RECEIVED"`
    - [ ] `EVENT_ON_MISS = "ON_MISS"`
    - [ ] `EVENT_ON_CRITICAL_HIT = "ON_CRITICAL_HIT"`
    - [ ] `EVENT_ON_KILL = "ON_KILL"`
    - [ ] `EVENT_ON_DEATH = "ON_DEATH"`
    - [ ] `EVENT_ON_STATUS_EFFECT_APPLIED = "ON_STATUS_EFFECT_APPLIED"`
    - [ ] `EVENT_ON_STATUS_EFFECT_REMOVED = "ON_STATUS_EFFECT_REMOVED"`
    - [ ] `EVENT_ON_STATUS_EFFECT_EXPIRED = "ON_STATUS_EFFECT_EXPIRED"`
    - [ ] `EVENT_ON_STATUS_EFFECT_TICK = "ON_STATUS_EFFECT_TICK"`
    - [ ] `EVENT_ON_ALLY_DEATH = "ON_ALLY_DEATH"`
    - [ ] `EVENT_ON_ENEMY_DEATH = "ON_ENEMY_DEATH"`
    - [ ] `EVENT_ON_COMBATANT_ADDED_TO_BATTLE = "ON_COMBATANT_ADDED_TO_BATTLE"` (例如召喚物)
    - [ ] `EVENT_ON_HP_CHANGE = "ON_HP_CHANGE"`
    - [ ] `EVENT_ON_MP_CHANGE = "ON_MP_CHANGE"`
- [ ] **定義事件數據結構 (使用 `TypedDict` 或 `pydantic.BaseModel`)：**
    - [ ] 為 `RPG_10_Event_System_Details.md` 中列出的每個事件類型創建對應的數據類/字典結構，包含其 `event_data` 字段。
    - [ ] 例如：
        ```python
        from typing import TypedDict, Optional, List, Dict, Any
        # from rpg_system.battle_system.models import Combatant, SkillInstance, StatusEffectInstance # Use strings for TYPE_CHECKING

        class EventOnBattleStartData(TypedDict):
            battle_context: 'Battle'

        class EventOnTurnStartData(TypedDict):
            battle_context: 'Battle'
            current_combatant: 'Combatant'
            turn_number: int

        class EventOnDealDamageData(TypedDict):
            caster: 'Combatant'
            target: 'Combatant'
            skill_instance: Optional['SkillInstance'] # None if damage from status effect tick etc.
            status_effect_instance: Optional['StatusEffectInstance'] # None if damage from skill etc.
            damage_amount: float
            damage_type: str
            is_crit: bool
            is_miss: bool # Should be false if ON_DEAL_DAMAGE, true for ON_MISS
            source_element: Optional[str]
            skill_tags: Optional[List[str]]
            # ... any other relevant data from RPG_10
        ```
    - [ ] 完成所有在 `RPG_10` 中定義的事件數據結構。

## 2. PassiveTriggerHandler 服務定義

在 `rpg_system/battle_system/handlers/passive_trigger_handler.py` 中創建 `PassiveTriggerHandler` 類。

- [ ] **創建 `PassiveTriggerHandler` 類：**
    - [ ] **依賴注入：**
        - [ ] `__init__(self, effect_applier: 'EffectApplier', formula_evaluator: 'FormulaEvaluator', config_loader: 'ConfigLoader')`
    - [ ] **主要方法：** `handle_event(self, event_type: str, event_data: Dict[str, Any], battle_context: 'Battle')`
        - [ ] **參數：**
            - `event_type`: 觸發的事件類型 (e.g., `events.EVENT_ON_TAKE_DAMAGE`)。
            - `event_data`: 與事件類型對應的數據字典/對象。
            - `battle_context`: `Battle` 實例。
        - [ ] **返回值：** (可選) 可能返回一個列表，包含哪些被動技能被觸發並執行了，或者直接修改 `battle_context`。
        - [ ] **核心邏輯 (參考 `RPG_07_PassiveTriggerHandler.md` - Workflow)：**
            - [ ] **1. 收集潛在的被動技能：**
                - [ ] 遍歷戰場上所有存活的 `Combatant` (`battle_context.get_all_alive_combatants()`)。
                - [ ] 對於每個 `combatant`，收集其所有被動技能實例 (`common_passives` 和 `innate_passive_skill`)。
            - [ ] **2. 過濾和排序被動技能：**
                - [ ] `triggered_passives_to_execute = []`
                - [ ] `for combatant_with_passive in all_combatants:`
                    - [ ] `for passive_skill_instance in combatant_with_passive.all_passives:` (需要一個輔助方法 `combatant.get_all_passives()`)
                        - [ ] `passive_config = passive_skill_instance.get_definition(self.config_loader)` (需要處理星級/技能等級來獲取正確的 `PassiveEffectBlock`)
                        - [ ] `for effect_block in passive_config.get_effects_for_current_level():` (假設 `get_effects_for_current_level` 能拿到正確等級的 `PassiveEffectBlock` 列表)
                            - [ ] **a. 檢查事件類型匹配 (`effect_block.trigger_condition.type`)：**
                                - [ ] `if effect_block.trigger_condition.type == event_type:`
                                    - [ ] (繼續檢查其他條件)
                            - [ ] **b. 檢查觸發者 (`trigger_condition.source`)：** (來自 `RPG_07` - 觸發源)
                                - [ ] 根據 `trigger_condition.source` (e.g., `SELF`, `ALLY`, `ENEMY`, `ANY`) 和 `event_data` 中的行動者/受影響者，判斷此被動是否應該被當前 combatant 的這個被動技能監聽。
                                    *   例如，如果 `event_type` 是 `ON_TAKE_DAMAGE`，`event_data` 包含 `target` (受傷者)。如果 `trigger_condition.source == "SELF"`，則只有當 `combatant_with_passive == event_data['target']` 時才繼續。
                            - [ ] **c. 檢查子類型 (`trigger_condition.sub_type`) (如果適用)：**
                                - [ ] 例如 `ON_TAKE_DAMAGE` 的 `sub_type` 可能是 `"PHYSICAL"`, `"MAGICAL"`, `"FIRE"`。需要與 `event_data` 中的傷害類型或技能標籤匹配。
                            - [ ] **d. 檢查觸發機率 (`trigger_condition.chance_formula`)：**
                                - [ ] `context_vars = {**event_data, "caster": combatant_with_passive, "passive_skill": passive_skill_instance, ...}`
                                - [ ] `chance = self.formula_evaluator.evaluate(effect_block.trigger_condition.chance_formula, context_vars)`
                                - [ ] `if battle_context._rng.random() < chance:` (繼續檢查)
                            - [ ] **e. 檢查額外條件 (`trigger_condition.additional_conditions`)：**
                                - [ ] `all_additional_met = True`
                                - [ ] `for add_cond in effect_block.trigger_condition.additional_conditions:`
                                    - [ ] `if not self.formula_evaluator.evaluate(add_cond.formula, context_vars): all_additional_met = False; break`
                                - [ ] `if all_additional_met:`
                                    - [ ] `triggered_passives_to_execute.append((passive_skill_instance, effect_block, combatant_with_passive, passive_config.get('trigger_priority', 0)))`
            - [ ] **3. 按優先級排序 (`trigger_priority`)：**
                - [ ] `triggered_passives_to_execute.sort(key=lambda x: x[3], reverse=True)` (高優先級先執行)
            - [ ] **4. 執行被動效果：**
                - [ ] `for skill_instance, effect_block, owner_combatant, priority in triggered_passives_to_execute:`
                    - [ ] **a. 處理 `trigger_once_per_battle`：**
                        - [ ] 需要在 `Battle` 或 `Combatant` 的某處記錄此被動的此 `EffectBlock` 是否已觸發過。
                        - [ ] 如果已觸發且 `trigger_once_per_battle` 為 `True`，則跳過。
                    - [ ] **b. 確定目標：**
                        - [ ] `if effect_block.target_override:`
                            - [ ] `actual_targets = self.target_selector.select_targets(owner_combatant, effect_block.target_override, battle_context, self.formula_evaluator, self.config_loader)`
                        - [ ] `else:` (沒有覆蓋時，被動的目標如何確定？參考 `RPG_07` - 目標確定。通常是 `event_data` 中的關鍵角色，或被動擁有者自身)
                            - [ ] `default_target_key = effect_block.trigger_condition.default_target_from_event_data` (假設有此配置)
                            - [ ] `actual_targets = [event_data.get(default_target_key)] if event_data.get(default_target_key) else [owner_combatant]`
                            - [ ] (需要仔細設計這裡的默認目標邏輯)
                    - [ ] **c. 應用效果：**
                        - [ ] `self.effect_applier.apply_effect_definitions(owner_combatant, actual_targets, effect_block.effect_definitions, battle_context, source_skill_tags=passive_skill_instance.get_definition(self.config_loader).get('tags',[]), source_skill_instance=skill_instance, custom_vars_from_source=event_data)`
                    - [ ] **d. 記錄觸發 (用於 `trigger_once_per_battle`)**
                    - [ ] **e. 記錄戰鬥日誌** (表明哪個被動被觸發，對誰產生了什麼效果)

## 3. 集成事件派發到戰鬥流程中

在 `Battle` 類 (`models/battle.py`) 和其他相關的處理器 (`EffectApplier`, `DamageHandler`, `StatusEffectHandler`, `Combatant`) 中，在適當的時機調用 `PassiveTriggerHandler.handle_event`。

- [ ] **`Battle.start()`:**
    - [ ] 派發 `EVENT_ON_BATTLE_START`。
- [ ] **`Battle.end_battle()` (假設有此方法):**
    - [ ] 派發 `EVENT_ON_BATTLE_END`。
- [ ] **`Battle.next_turn()` (或類似的開始一個新戰鬥單位行動的方法):**
    - [ ] 在一個戰鬥單位行動開始前，派發 `EVENT_ON_TURN_START` 給該單位。
- [ ] **`Combatant.apply_turn_end_effects()` (或類似的結束一個戰鬥單位行動的方法):**
    - [ ] 在一個戰鬥單位行動結束後，派發 `EVENT_ON_TURN_END` 給該單位。
- [ ] **`EffectApplier.apply_skill_effects()` (或 `Battle.process_action()`):**
    - [ ] 在技能施放前派發 `EVENT_ON_BEFORE_SKILL_USE`。
    - [ ] 在技能效果完全結算後派發 `EVENT_ON_AFTER_SKILL_USE`。
- [ ] **`DamageHandler.calculate_and_apply_damage()` (或 `EffectApplier` 中處理傷害的邏輯):**
    - [ ] 成功造成傷害後，派發 `EVENT_ON_DEAL_DAMAGE` 和 `EVENT_ON_TAKE_DAMAGE`。
    - [ ] 如果未命中，派發 `EVENT_ON_MISS`。
    - [ ] 如果暴擊，派發 `EVENT_ON_CRITICAL_HIT` (可與 `ON_DEAL_DAMAGE` 一同發出，或作為其 `event_data` 的一部分)。
    - [ ] 如果目標死亡，派發 `EVENT_ON_KILL` (給攻擊者) 和 `EVENT_ON_DEATH` (給死亡者)。
- [ ] **`EffectApplier` 中處理治療的邏輯:**
    - [ ] 成功治療後，派發 `EVENT_ON_HEAL_DEALT` 和 `EVENT_ON_HEAL_RECEIVED`。
- [ ] **`StatusEffectHandler.apply_status_effect()`:**
    - [ ] 派發 `EVENT_ON_STATUS_EFFECT_APPLIED`。
- [ ] **`StatusEffectHandler.handle_status_effect_expiration()` / `remove_status_effect()`:**
    - [ ] 派發 `EVENT_ON_STATUS_EFFECT_REMOVED` 或 `EVENT_ON_STATUS_EFFECT_EXPIRED`。
- [ ] **`StatusEffectHandler.tick_status_effect()` (如果 tick 產生效果):**
    - [ ] 派發 `EVENT_ON_STATUS_EFFECT_TICK`。
- [ ] **`Combatant.take_damage()` / `Combatant.heal()`:**
    - [ ] 派發 `EVENT_ON_HP_CHANGE`。
- [ ] **`Combatant.consume_mp()` / `Combatant.gain_mp()`:**
    - [ ] 派發 `EVENT_ON_MP_CHANGE`。
- [ ] **戰鬥單位死亡時 (`Combatant.take_damage` 導致死亡，或 `Battle.check_win_condition`):**
    - [ ] 為死者的每個盟友派發 `EVENT_ON_ALLY_DEATH`。
    - [ ] 為死者的每個敵人派發 `EVENT_ON_ENEMY_DEATH`。
- [ ] **(如果實現召喚) 當召喚物加入戰鬥時：**
    - [ ] 派發 `EVENT_ON_COMBATANT_ADDED_TO_BATTLE`。

**重要：** 事件派發的具體位置和 `event_data` 的組裝需要仔細考慮，確保所有相關信息都傳遞給了 `PassiveTriggerHandler`。
`Battle` 類應該有一個方法，例如 `Battle.dispatch_event(event_type, event_data)`，內部調用 `PassiveTriggerHandler` 實例的 `handle_event` 方法。

## 4. 單元測試

- [ ] **測試事件定義：** (簡單驗證常量和結構)
- [ ] **為 `PassiveTriggerHandler` 編寫詳細的單元測試：**
    - [ ] Mock 依賴 (`EffectApplier`, `FormulaEvaluator`, `ConfigLoader`, `BattleContext`, `Combatant`, `SkillInstance`)。
    - [ ] **測試被動技能的正確觸發：**
        - [ ] 基於不同的 `event_type`。
        - [ ] 基於 `trigger_condition.source` (SELF, ALLY, ENEMY)。
        - [ ] 基於 `sub_type` 過濾。
        - [ ] 基於 `chance_formula` (測試機率性觸發和必定觸發)。
        - [ ] 基於 `additional_conditions`。
        - [ ] 測試 `trigger_once_per_battle` 邏輯。
    - [ ] **測試觸發優先級：** 確保高優先級被動先執行。
    - [ ] **測試目標確定：**
        - [ ] `target_override` 的正確應用。
        - [ ] 默認目標的選擇邏輯。
    - [ ] **驗證效果應用：** 確保 `EffectApplier.apply_effect_definitions` 被正確參數調用。
    - [ ] **測試邊界情況：**
        - [ ] 沒有戰鬥單位或沒有被動技能。
        - [ ] 事件數據不完整。
        - [ ] 被動配置錯誤。
- [ ] **為事件派發邏輯編寫集成測試 (在 `Battle` 類或其他相關類的測試中)：**
    - [ ] 模擬一個戰鬥場景 (e.g., 造成傷害)。
    - [ ] 驗證相應的事件是否被派發給 `PassiveTriggerHandler`。
    - [ ] 驗證 `PassiveTriggerHandler` 是否正確處理了該事件並觸發了預期的被動技能。

## 5. 文檔與類型提示

- [ ] 為所有新創建的類、方法、事件常量和數據結構添加清晰的文檔字符串。
- [ ] 使用 Python 的類型提示，並通過 `TYPE_CHECKING` 塊處理循環依賴。

---
完成所有上述任務後，請勾選並更新 `RPG_IMPLEMENTATION_PLAN.md` 中的相應條目。 