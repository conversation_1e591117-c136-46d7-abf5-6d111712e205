# RPG 任務 04：核心領域模型

本文檔詳細列出了 RPG 系統核心領域模型的實現任務，主要集中在戰鬥系統的基礎數據結構。

**參考設計文檔：**
*   `RPG_04_Domain_Model_Battle_System.md` (1. `Battle`, 2. `Combatant`, 3. `SkillInstance`, 6. `BattleLogEntry`)
*   `RPG_02_Configuration_Files.md` (用於理解模型中引用的配置ID)
*   `RPG_08_Glossary.md` (術語定義)

**目標位置：** 主要在 `rpg_system/battle_system/models/` 目錄下。

## 1. SkillInstance 模型

在 `rpg_system/battle_system/models/skill_instance.py` 中定義 `SkillInstance` 類。

- [ ] **定義 `SkillType` 枚舉 (Enum)：**
    - [ ] `ACTIVE`
    - [ ] `PASSIVE`
    - [ ] `INNATE_PASSIVE`
    - [ ] `PRIMARY_ATTACK`
- [ ] **定義 `SkillInstance` 類：**
    - [ ] **屬性 (Attributes)：**
        - [ ] `skill_id: str`
        - [ ] `skill_type: SkillType`
        - [ ] `current_level: int`
        - [ ] `current_cooldown: int = 0` (默認為0)
    - [ ] **方法 (Methods)：**
        - [ ] `__init__(self, skill_id: str, skill_type: SkillType, current_level: int, current_cooldown: int = 0)`
        - [ ] `get_definition(self, all_configs: 'ConfigLoader') -> dict`: (存根實現或初步實現)
            *   根據 `skill_type` 決定從 `all_configs` (ConfigLoader 實例) 的哪個配置字典中查找 (e.g., `all_configs.active_skills`, `all_configs.passive_skills`, `all_configs.innate_passive_skills`).
            *   根據 `skill_id` 獲取技能的基礎配置。
            *   **關鍵邏輯：** 根據 `current_level` (對於天賦，則基於傳入的 `star_level` 變量，此處可能需要調整 `get_definition` 的參數或 `SkillInstance` 的設計，使其能感知 `star_level` 或從 `Combatant` 獲取) 從技能配置的 `effects_by_level` 或 `effects_by_star_level` 中提取正確等級/檔位的效果定義、MP消耗、基礎冷卻等。
            *   返回包含該等級/檔位具體定義的字典 (包括 `effect_definitions`, `mp_cost`, `cooldown_turns` 等)。
            *   **思考：** `all_configs` 的類型提示，這裡暫用字符串 `'ConfigLoader'` 以避免循環導入，實際應為 `rpg_system.config.loader.ConfigLoader`。
        - [ ] `is_usable(self, caster_mp: int, config_loader: 'ConfigLoader') -> bool`: (存根實現或初步實現)
            *   僅對 `ACTIVE` 和 `PRIMARY_ATTACK` 技能有意義。
            *   檢查 `self.current_cooldown == 0`。
            *   調用 `self.get_definition(config_loader)` 獲取技能定義，檢查 `caster_mp` 是否足夠支付 `mp_cost`。
            *   普攻 (`PRIMARY_ATTACK`) 通常不消耗MP且無冷卻，或者其定義特殊。
        - [ ] `put_on_cooldown(self, config_loader: 'ConfigLoader')`: (存根實現或初步實現)
            *   調用 `self.get_definition(config_loader)` 獲取技能定義中的基礎冷卻 `cooldown_turns`。
            *   設置 `self.current_cooldown = cooldown_turns`。
        - [ ] `tick_cooldown(self)`: (簡單實現)
            *   `if self.current_cooldown > 0: self.current_cooldown -= 1`

## 2. Combatant 模型

在 `rpg_system/battle_system/models/combatant.py` 中定義 `Combatant` 類。

- [ ] **定義 `Combatant` 類：**
    - [ ] **屬性 (Attributes)：**
        - [ ] `instance_id: str` (唯一實例ID, e.g., `gacha_user_collections.id` 或臨時生成的怪物ID)
        - [ ] `definition_id: str` (e.g., `card_id` 或 `monster_id`)
        - [ ] `name: str`
        - [ ] `is_player_side: bool`
        - [ ] `rpg_level: int`
        - [ ] `star_level: int` (0-35)
        - [ ] `skill_order_preference: List[str]` (主動技能ID列表，按優先順序)
        - [ ] `primary_attack_skill: SkillInstance`
        - [ ] `active_skills: List[SkillInstance]`
        - [ ] `innate_passive_skill: Optional[SkillInstance]` (天賦技能實例，可能沒有)
        - [ ] `common_passives: List[SkillInstance]`
        - [ ] `position: int = 0` (默認0，或根據需要調整)
        - [ ] **動態戰鬥屬性 (初始化時計算，戰鬥中可變)：**
            - [ ] `max_hp: float`
            - [ ] `current_hp: float`
            - [ ] `max_mp: float`
            - [ ] `current_mp: float`
            - [ ] `base_stats_from_config: Dict[str, float]` (存儲從卡牌/怪物JSON直接讀取的基礎屬性，未經成長)
            - [ ] `calculated_rpg_stats: Dict[str, float]` (存儲基於RPG等級、星級等計算後的靜態屬性，未計入戰鬥buff/debuff)
            - [ ] `current_battle_stats: Dict[str, float]` (實際戰鬥中使用的屬性，受buff/debuff影響，例如 `{"patk": 100.0, "pdef": 50.0, ...}`)
            - [ ] `status_effects: List['StatusEffectInstance']` (存儲當前激活的狀態效果實例，使用字符串避免循環導入)
    - [ ] **方法 (Methods) - 部分為存根，待後續任務填充完整邏輯：**
        - [ ] `__init__(...)`: 初始化所有來自配置或玩家數據的靜態/初始屬性。
            *   `current_hp` 和 `current_mp` 通常在初始化時等於 `max_hp` 和 `max_mp`。
        - [ ] `calculate_initial_combat_stats(self, config_loader: 'ConfigLoader', attribute_calculator: 'AttributeCalculator')`: (調用 `AttributeCalculator`)
            *   此方法應在 `Combatant` 實例化後被外部調用 (例如在 `BattleCoordinatorService` 中)。
            *   調用 `attribute_calculator.calculate_attributes(...)` (詳見 `RPG_TASK_05`) 獲得計算後的 `max_hp`, `max_mp`, 和 `calculated_rpg_stats`。
            *   將這些值賦給 `self.max_hp`, `self.current_hp`, `self.max_mp`, `self.current_mp`, 和 `self.current_battle_stats` (初始時 `current_battle_stats` 等於 `calculated_rpg_stats`)。
        - [ ] `take_damage(self, amount: float, damage_type: str, is_crit: bool, battle_context: 'Battle')`: 更新 `current_hp`。
        - [ ] `heal(self, amount: float, battle_context: 'Battle')`: 更新 `current_hp`。
        - [ ] `consume_mp(self, amount: int)`: 更新 `current_mp`。
        - [ ] `add_status_effect(self, status_effect_instance: 'StatusEffectInstance', battle_context: 'Battle')`
        - [ ] `remove_status_effect(self, status_effect_id_to_remove: str, battle_context: 'Battle')`
        - [ ] `get_available_action(self, battle_context: 'Battle', config_loader: 'ConfigLoader') -> Tuple[str, List[str]]`: (初步邏輯)
            *   遍歷 `skill_order_preference` 中的 `active_skill_ids`。
            *   對每個 `skill_id`，找到對應的 `SkillInstance`。
            *   調用 `skill_instance.is_usable(self.current_mp, config_loader)`。
            *   返回第一個可用的 `skill_id` 和一個初步的目標建議 (e.g., 敵方第一個單位)。
            *   如果都不可用，返回 `self.primary_attack_skill.skill_id` 和目標建議。
        - [ ] `apply_turn_start_effects(self, battle_context: 'Battle', config_loader: 'ConfigLoader')`: (存根)
            *   調用 `tick_cooldowns()`。
            *   調用 `tick_status_effects(battle_context, config_loader, "START")`。
            *   (未來) 觸發回合開始型被動。
        - [ ] `apply_turn_end_effects(self, battle_context: 'Battle', config_loader: 'ConfigLoader')`: (存根)
            *   調用 `tick_status_effects(battle_context, config_loader, "END")`。
            *   (未來) 觸發回合結束型被動。
        - [ ] `tick_cooldowns(self)`: 遍歷 `self.active_skills` 和 `self.primary_attack_skill`，調用其 `tick_cooldown()`。
        - [ ] `tick_status_effects(self, battle_context: 'Battle', config_loader: 'ConfigLoader', tick_phase: Literal["START", "END"])`: (存根，`StatusEffectInstance` 實現後填充)
            *   遍歷 `self.status_effects`。
            *   減少持續時間，處理過期。
            *   根據 `tick_phase` 和狀態效果的 `tick_at_turn_start`/`tick_at_turn_end` 觸發其 `effect_definitions_per_tick`。
        - [ ] `is_alive(self) -> bool`: `self.current_hp > 0`。
        - [ ] `can_act(self, battle_context: 'Battle') -> bool`: (初步邏輯，檢查是否有 `CANNOT_ACT` 狀態效果)。
        - [ ] `get_skill_instance(self, skill_id: str) -> Optional[SkillInstance]`: 從 `active_skills`, `primary_attack_skill`, `innate_passive_skill`, `common_passives` 中查找。
        - [ ] `get_stat(self, stat_name: str) -> float`: 從 `self.current_battle_stats` 獲取屬性值。
        - [ ] `recalculate_battle_stats_from_buffs_debuffs(self, config_loader: 'ConfigLoader')`: (存根，`StatusEffectInstance` 實現後填充)
            *   核心邏輯：基於 `self.calculated_rpg_stats` 和所有激活的 `status_effects` 中的 `STAT_MODIFICATION` 類型的效果，重新計算 `self.current_battle_stats`。
            *   參考 `RPG_02_Configuration_Files.md` 中關於狀態效果屬性疊加的規則。

## 3. BattleStatus 枚舉

在 `rpg_system/battle_system/models/battle.py` (或一個共享的 `enums.py`) 中定義 `BattleStatus` 枚舉。

- [ ] **定義 `BattleStatus` 枚舉 (Enum)：**
    - [ ] `PENDING`
    - [ ] `IN_PROGRESS`
    - [ ] `PLAYER_WIN`
    - [ ] `MONSTER_WIN`
    - [ ] `DRAW`

## 4. BattleLogEntry 模型

在 `rpg_system/battle_system/models/battle_log.py` (或直接在 `battle.py`) 中定義 `BattleLogEntry` 數據類或 Pydantic 模型。

- [ ] **定義 `BattleLogEntryTargetEffect` (輔助，可選)：**
    - [ ] `target_instance_id: str`
    - [ ] `target_name: str`
    - [ ] `effects_received: List[str]` (效果描述文本列表)
    - [ ] `damage_taken: Optional[float] = None`
    - [ ] `healed_amount: Optional[float] = None`
    - [ ] `was_crit: Optional[bool] = None`
    - [ ] `was_miss: Optional[bool] = None`
    - [ ] `status_applied: Optional[str] = None` (status_effect_id 或 name)
- [ ] **定義 `BattleLogEntry` 類 (使用 `dataclasses.dataclass` 或 `pydantic.BaseModel`)：**
    - [ ] `turn_number: int`
    - [ ] `timestamp: datetime = field(default_factory=datetime.utcnow)`
    - [ ] `actor_type: Literal["PLAYER_CARD", "MONSTER", "SYSTEM"]`
    - [ ] `actor_instance_id: Optional[str] = None`
    - [ ] `actor_name: Optional[str] = None`
    - [ ] `action_type: Literal["SKILL_CAST", "PASSIVE_PROC", "STATUS_EFFECT_TICK", "BATTLE_EVENT", "SYSTEM_MESSAGE"]`
    - [ ] `action_name: Optional[str] = None` (技能名, 效果名, 事件名)
    - [ ] `targets_details: Optional[List[BattleLogEntryTargetEffect]] = None`
    - [ ] `message_template_key: Optional[str] = None` (用於本地化)
    - [ ] `raw_message: Optional[str] = None` (如果不用模板，直接提供消息)
    - [ ] `log_details: Optional[Dict[str, Any]] = None` (額外數據)

## 5. Battle 模型

在 `rpg_system/battle_system/models/battle.py` 中定義 `Battle` 類。

- [ ] **定義 `Battle` 類：**
    - [ ] **屬性 (Attributes)：**
        - [ ] `battle_id: str = field(default_factory=lambda: str(uuid.uuid4()))`
        - [ ] `player_team: List[Combatant]`
        - [ ] `monster_team: List[Combatant]`
        - [ ] `current_turn: int = 0`
        - [ ] `battle_log: List[BattleLogEntry]`
        - [ ] `battle_status: BattleStatus = BattleStatus.PENDING`
        - [ ] `rng_seed: Optional[int] = None` (用於可複現戰鬥)
        - [ ] `_rng: random.Random` (內部隨機數生成器實例，使用 `rng_seed` 初始化)
        - [ ] `combatant_queue: List[str]` (按行動順序排列的 `Combatant.instance_id` 列表)
        - [ ] `_acting_combatant_index: int = -1` (指向 `combatant_queue` 的當前行動者索引)
    - [ ] **方法 (Methods) - 部分為存根，待後續任務填充完整邏輯：**
        - [ ] `__init__(self, player_team: List[Combatant], monster_team: List[Combatant], rng_seed: Optional[int] = None)`
            *   初始化隊伍、日誌、狀態、隨機數生成器。
        - [ ] `start(self, config_loader: 'ConfigLoader')`: (存根)
            *   設置 `battle_status = BattleStatus.IN_PROGRESS`。
            *   `self.current_turn = 1`。
            *   計算初始行動順序 (`_calculate_initial_combatant_queue()`)。
            *   `_acting_combatant_index = 0`。
            *   (未來) 觸發戰鬥開始型被動/天賦 (ON_BATTLE_START 事件)。
            *   記錄戰鬥開始日誌。
        - [ ] `_calculate_initial_combatant_queue(self)`: (存根)
            *   收集所有存活的 `player_team` 和 `monster_team` 中的 `Combatant`。
            *   根據 `spd` 屬性降序排序。
            *   若 `spd` 相同，可加入次要排序規則 (e.g., 玩家優先，或隨機)。
            *   填充 `self.combatant_queue` 為排序後的 `instance_id` 列表。
        - [ ] `next_turn(self)`: (存根)
            *   推進 `_acting_combatant_index`。
            *   如果索引超出 `combatant_queue` 長度，則表示一個邏輯回合結束：
                *   `self.current_turn += 1`。
                *   重新計算行動順序 (`_calculate_initial_combatant_queue()`)，因為速度可能變化。
                *   `_acting_combatant_index = 0`。
            *   如果 `combatant_queue` 為空 (e.g. 所有人都死了)，處理戰鬥結束。
        - [ ] `get_acting_combatant(self) -> Optional[Combatant]`: (存根)
            *   如果 `_acting_combatant_index` 有效且 `combatant_queue` 非空，返回隊列中對應的 `Combatant` 對象 (需要遍歷隊伍查找 ID)。
        - [ ] `process_action(self, caster: Combatant, skill_id: str, target_ids: List[str], config_loader: 'ConfigLoader', effect_applier: 'EffectApplier')`: (存根)
            *   (核心戰鬥邏輯，將在 `RPG_TASK_08` 中詳細實現)
        - [ ] `check_win_condition(self)`: (存根)
            *   檢查 `player_team` 是否全部 `is_alive() == False` -> `MONSTER_WIN`。
            *   檢查 `monster_team` 是否全部 `is_alive() == False` -> `PLAYER_WIN`。
            *   (可選) 檢查是否達到最大回合數 -> `DRAW`。
            *   更新 `self.battle_status`。
        - [ ] `add_log_entry(self, entry: BattleLogEntry)` 或 `add_log_message(self, message: str, ...)`: 添加日誌。
        - [ ] `get_combatant_by_id(self, instance_id: str) -> Optional[Combatant]`: 輔助方法，根據ID查找戰場上的任何戰鬥單位。
        - [ ] `get_all_alive_combatants(self) -> List[Combatant]`
        - [ ] `get_all_alive_enemies_of(self, combatant: Combatant) -> List[Combatant]`
        - [ ] `get_all_alive_allies_of(self, combatant: Combatant) -> List[Combatant]`

## 6. 類型提示與導入

- [ ] 在模型文件中使用 `from typing import List, Optional, Dict, Any, Tuple, Literal` 等。
- [ ] 處理循環導入問題：對其他模塊中的類型，在類型提示時使用字符串形式 (e.g., `battle_context: 'Battle'`)，並在文件頂部使用 `if TYPE_CHECKING:`塊來實際導入這些類型以供靜態分析器使用。
    ```python
    from typing import TYPE_CHECKING
    if TYPE_CHECKING:
        from rpg_system.config.loader import ConfigLoader
        from rpg_system.battle_system.models.battle import Battle # 示例
        from rpg_system.battle_system.models.status_effect import StatusEffectInstance # 示例
        from rpg_system.battle_system.services import AttributeCalculator # 示例
        from rpg_system.battle_system.handlers import EffectApplier # 示例
    ```

---
完成所有上述任務後，請勾選並更新 `RPG_IMPLEMENTATION_PLAN.md` 中的相應條目。 