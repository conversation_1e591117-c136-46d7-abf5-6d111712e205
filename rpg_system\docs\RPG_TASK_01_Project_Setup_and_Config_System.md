# RPG 任務 01：項目設置與配置系統

本文檔詳細列出了 RPG 系統實現的第一個任務集：項目結構設置、配置系統的建立以及 Pydantic 模型的定義。

**參考設計文檔：**
*   `RPG_01_System_Architecture.md` (V. 建議的專案文件結構)
*   `RPG_02_Configuration_Files.md` (配置管理與驗證策略, 附錄：使用 Pydantic 定義 active_skills.json 的模型示例)

## 1. 項目結構初始化

根據 `RPG_01_System_Architecture.md` 中 "V. 建議的專案文件結構" 創建以下目錄結構：

- [ ] 創建 `rpg_system/` 根目錄
- [ ] 創建 `rpg_system/battle_system/`
    - [ ] 創建 `rpg_system/battle_system/models/`
    - [ ] 創建 `rpg_system/battle_system/handlers/`
    - [ ] 創建 `rpg_system/battle_system/services/`
- [ ] 創建 `rpg_system/config/`
    - [ ] 創建 `rpg_system/config/pydantic_models/`
    - [ ] 創建 `rpg_system/config/data/` (並將設計文檔中提及的JSON配置文件先創建為空文件，如 `cards.json`, `active_skills.json` 等)
    - [ ] 創建 `rpg_system/config/loader.py` (空文件)
- [ ] 創建 `rpg_system/services/`
    - [ ] 創建 `rpg_system/services/battle_coordinator_service.py` (空文件)
    - [ ] 創建 `rpg_system/services/player_card_management_service.py` (空文件)
    - [ ] 創建 `rpg_system/services/player_skill_management_service.py` (空文件)
- [ ] 創建 `rpg_system/repositories/`
    - [ ] 創建 `rpg_system/repositories/player_collection_rpg_repository.py` (空文件)
    - [ ] 創建 `rpg_system/repositories/global_skill_repository.py` (空文件)
    - [ ] 創建 `rpg_system/repositories/user_progress_rpg_repository.py` (空文件)
- [ ] 創建 `rpg_system/cogs/`
    - [ ] 創建 `rpg_system/cogs/battle_cog.py` (空文件)
    - [ ] 創建 `rpg_system/cogs/skill_management_cog.py` (空文件)
- [ ] 創建 `rpg_system/views/`
    - [ ] 創建 `rpg_system/views/embeds/`
        - [ ] 創建 `rpg_system/views/embeds/battle_embeds.py` (空文件)
    - [ ] 創建 `rpg_system/views/formatters/`
- [ ] 創建 `rpg_system/formula_engine/`
    - [ ] 創建 `rpg_system/formula_engine/evaluator.py` (空文件)
- [ ] 創建 `rpg_system/docs/` (並將現有的 `docs/RPG/` 下所有 `.md` 文件移動到此處)
- [ ] 創建 `rpg_system/utils/`
- [ ] 創建 `scripts/generation/` (如果尚不存在)

## 2. Pydantic 模型定義

在 `rpg_system/config/pydantic_models/` 目錄下為每個核心 JSON 配置文件創建對應的 Pydantic 模型。

- [ ] **`active_skills_models.py`**: (參考 `RPG_02_Configuration_Files.md` 附錄示例)
    - [ ] `ModifierCondition`
    - [ ] `ModifierConditionGroup`
    - [ ] `Modifier` (包含其 `@validator`)
    - [ ] `StatModificationItem`
    - [ ] `EffectDefinition` (包含其 `@validator`)
    - [ ] `TargetLogicParams`
    - [ ] `TargetLogicCondition`
    - [ ] `TargetLogicDetail`
    - [ ] `ActiveSkillEffectLevel`
    - [ ] `ActiveSkillConfig` (包含其 `@validator`)
    - [ ] 主模型: `Dict[str, ActiveSkillConfig]` 的 Pydantic 表示 (例如 `AllActiveSkillsConfig`) - (可選，或者直接在 ConfigLoader 中處理字典)
- [ ] **`passive_skills_models.py`**:
    - [ ] `PassiveEffectBlock` (類似 `innate_passive_skills_models.py` 的 `PassiveEffectBlock`，但公式變量使用 `skill_level`)
        - [ ] 內含 `trigger_condition` (參考 `innate_passive_skills_models.py`)
        - [ ] 內含 `target_override` (參考 `innate_passive_skills_models.py`)
        - [ ] 內含 `effect_definitions` (使用 `active_skills_models.EffectDefinition`)
    - [ ] `PassiveSkillConfig`
        - [ ] `name: str`
        - [ ] `description_template: str`
        - [ ] `description_template_by_level: Optional[Dict[str, str]] = None`
        - [ ] `skill_rarity: int = Field(..., ge=1, le=7)`
        - [ ] `max_level: int = Field(..., ge=1)`
        - [ ] `xp_gain_on_sacrifice: Optional[int] = Field(None, ge=0)`
        - [ ] `xp_to_next_level_config: Optional[Dict[str, Any]] = None`
        - [ ] `tags: Optional[List[str]] = None`
        - [ ] `effects_by_level: Dict[str, List[PassiveEffectBlock]]` (鍵為全局技能等級)
        - [ ] `trigger_priority: Optional[int] = 0` (新增，參考 `RPG_07_PassiveTriggerHandler.md`)
    - [ ] 主模型: `Dict[str, PassiveSkillConfig]`
- [ ] **`innate_passive_skills_models.py`**:
    - [ ] `TriggerConditionParams` (用於 `trigger_condition` 的 `params`)
    - [ ] `AdditionalCondition` (用於 `trigger_condition.additional_conditions`)
    - [ ] `TriggerCondition` (包含 `type`, `sub_type`, `chance_formula`, `trigger_once_per_battle`, `params`, `additional_conditions`)
    - [ ] `TargetOverride`
    - [ ] `PassiveEffectBlock`
        - [ ] `trigger_condition: TriggerCondition`
        - [ ] `target_override: Optional[TargetOverride] = None`
        - [ ] `effect_definitions: List[active_skills_models.EffectDefinition]`
    - [ ] `InnatePassiveSkillConfig`
        - [ ] `name: str`
        - [ ] `description_template: Optional[str] = None`
        - [ ] `description_template_by_star_level: Optional[Dict[str, str]] = None`
        - [ ] `skill_rarity: int = Field(..., ge=1, le=7)`
        - [ ] `tags: Optional[List[str]] = None`
        - [ ] `effects_by_star_level: Dict[str, List[PassiveEffectBlock]]` (鍵為卡牌培養星級)
    - [ ] 主模型: `Dict[str, InnatePassiveSkillConfig]`
- [ ] **`cards_models.py`**:
    - [ ] `BaseStats`
    - [ ] `GrowthPerRPGLevel`
    - [ ] `DefaultPassiveSlot`
    - [ ] `CardConfig`
        - [ ] `innate_passive_skill_id: str`
        - [ ] `primary_attack_skill_id: str`
        - [ ] `default_active_skill_slot_1_id: Optional[str] = None`
        - [ ] `default_active_skill_slot_2_id: Optional[str] = None`
        - [ ] `default_active_skill_slot_3_id: Optional[str] = None`
        - [ ] `passive_skill_slots: int`
        - [ ] `default_passives_on_acquire: Optional[List[DefaultPassiveSlot]] = None`
        - [ ] `base_stats: BaseStats`
        - [ ] `growth_per_rpg_level: GrowthPerRPGLevel`
        - [ ] `star_level_effects_key: Optional[str] = None`
    - [ ] 主模型: `Dict[str, CardConfig]`
- [ ] **`status_effects_models.py`**:
    - [ ] `StatusEffectConfig`
        - [ ] `name: str`
        - [ ] `icon_key: Optional[str] = None`
        - [ ] `is_buff: bool`
        - [ ] `max_stacks: Optional[int] = Field(1, ge=1)`
        - [ ] `duration_type: Literal["TURNS", "INFINITE", "UNTIL_TRIGGERED"]`
        - [ ] `default_duration: Optional[int] = Field(None, ge=0)`
        - [ ] `tick_at_turn_start: Optional[bool] = False`
        - [ ] `tick_at_turn_end: Optional[bool] = False`
        - [ ] `effect_definitions_on_apply: Optional[List[active_skills_models.EffectDefinition]] = None`
        - [ ] `effect_definitions_per_tick: Optional[List[active_skills_models.EffectDefinition]] = None`
        - [ ] `effect_definitions_on_expire: Optional[List[active_skills_models.EffectDefinition]] = None`
        - [ ] `effect_definitions_triggered: Optional[List[active_skills_models.EffectDefinition]] = None` # 結構可能需要細化，或與被動的 `PassiveEffectBlock` 類似
        - [ ] `special_flags: Optional[List[Literal[
            "CANNOT_ACT", "DISPELLABLE", "UNDISPELLABLE", 
            "STACKABLE_DURATION", "STACKABLE_INTENSITY", 
            "NON_STACKABLE_REFRESH_DURATION", "NON_STACKABLE_IGNORE",
            "PERSISTS_THROUGH_DEATH", "REMOVE_ON_DEATH", "LINKED_TO_CASTER"
        ]]] = None`
    - [ ] 主模型: `Dict[str, StatusEffectConfig]`
- [ ] **`effect_templates_models.py`**:
    - [ ] 主模型: `Dict[str, active_skills_models.EffectDefinition]`
- [ ] **`star_level_effects_models.py`**:
    - [ ] `StarLevelEffectDetail`
        - [ ] `additional_stats_flat: Optional[Dict[str, float]] = None`
        - [ ] `additional_stats_percent: Optional[Dict[str, float]] = None`
        - [ ] `unlock_passive_skill_slot: Optional[bool] = None`
        - [ ] `apply_self_effect_on_battle_start: Optional[List[active_skills_models.EffectDefinition]] = None`
    - [ ] `StarLevelEffectsConfig`: `Dict[str, StarLevelEffectDetail]` (鍵為培養星級)
    - [ ] 主模型: `Dict[str, StarLevelEffectsConfig]` (鍵為 `star_level_effects_key`)
- [ ] **`monsters_models.py`**:
    - [ ] `EquippedMonsterPassive`
        - [ ] `skill_id: str`
        - [ ] `level: int`
    - [ ] `MonsterConfig`
        - [ ] `name: str`
        - [ ] `hp: int`
        - [ ] `max_mp: int`
        - [ ] `mp_regen_per_turn: int`
        - [ ] `patk: int` 
        - [ ] `pdef: int`
        - [ ] `matk: int`
        - [ ] `mdef: int`
        - [ ] `spd: int`
        - [ ] `crit_rate: float`
        - [ ] `crit_dmg_multiplier: float`
        - [ ] `accuracy: float`
        - [ ] `evasion: float`
        - [ ] `primary_attack_skill_id: str`
        - [ ] `active_skill_order: List[str]`
        - [ ] `equipped_passives: List[EquippedMonsterPassive]`
    - [ ] 主模型: `Dict[str, MonsterConfig]`
- [ ] **`floors_models.py`**:
    - [ ] `FloorConfig`
        - [ ] `name: str`
        - [ ] `wins_required_to_advance: int`
        - [ ] `entry_cost_oil: int`
        - [ ] `first_clear_rewards_key: str`
        - [ ] `repeatable_rewards_per_win_key: str`
        - [ ] `possible_encounters: List[str]` # 指向 monster_groups.json 的鍵
    - [ ] 主模型: `Dict[str, FloorConfig]` (鍵為 floor_number 字符串)
- [ ] **`monster_groups_models.py`**:
    - [ ] `MonsterInGroup`
        - [ ] `monster_id: str`
        - [ ] `position: Optional[int] = None`
    - [ ] `MonsterGroupConfig`: `List[MonsterInGroup]`
    - [ ] 主模型: `Dict[str, MonsterGroupConfig]`
- [ ] **`reward_packages_models.py`**:
    - [ ] `RewardItem`
        - [ ] `type: str` # e.g., "CURRENCY", "ITEM", "CARD_XP", "GLOBAL_SKILL_XP"
        - [ ] `id: Optional[str] = None` # e.g., currency_id, item_id, skill_id
        - [ ] `quantity: int`
        - [ ] `quantity_formula: Optional[str] = None`
    - [ ] `RewardPackageConfig`: `List[RewardItem]`
    - [ ] 主模型: `Dict[str, RewardPackageConfig]`

## 3. ConfigLoader 實現

在 `rpg_system/config/loader.py` 中實現 `ConfigLoader` 類。

- [ ] 實現 `ConfigLoader` 類
    - [ ] `__init__(self, config_dir: str)`: 構造函數，接收配置數據文件夾路徑 (e.g., `rpg_system/config/data/`)
    - [ ] 存儲各類配置的屬性 (e.g., `self.cards: Dict[str, CardConfig]`, `self.active_skills: Dict[str, ActiveSkillConfig]`, etc.)
    - [ ] `load_all_configs(self)` 方法:
        - [ ] 遍歷 `config_dir` 下預期的 JSON 文件名。
        - [ ] 對於每個 JSON 文件：
            - [ ] 讀取文件內容。
            - [ ] 使用對應的 Pydantic 主模型進行解析和驗證。
            - [ ] 若驗證成功，將解析後的數據存儲到對應的類屬性中。
            - [ ] 若驗證失敗 (`pydantic.ValidationError`)，則捕獲異常，記錄詳細錯誤日誌，並可選擇阻止服務器啟動或標記該部分配置為不可用。
    - [ ] 提供獲取特定配置的方法，例如 `get_card_config(self, card_id: str) -> Optional[CardConfig]`。
    - [ ] (可選) 實現一個方法，在服務器啟動時將 `ConfigLoader` 實例化並加載所有配置，使其成為一個可全局訪問的單例或通過依賴注入提供給其他服務。

## 4. 初始化空JSON配置文件

在 `rpg_system/config/data/` 目錄下，為所有在 Pydantic 模型中定義的配置文件創建對應的空 JSON 文件 (如果之前未創建)。
例如：`active_skills.json`, `passive_skills.json`, `innate_passive_skills.json`, `cards.json`, `status_effects.json`, `effect_templates.json`, `star_level_effects.json`, `monsters.json`, `floors.json`, `monster_groups.json`, `reward_packages.json`.

- [ ] `active_skills.json` (初始為 `{}`)
- [ ] `passive_skills.json` (初始為 `{}`)
- [ ] `innate_passive_skills.json` (初始為 `{}`)
- [ ] `cards.json` (初始為 `{}`)
- [ ] `status_effects.json` (初始為 `{}`)
- [ ] `effect_templates.json` (初始為 `{}`)
- [ ] `star_level_effects.json` (初始為 `{}`)
- [ ] `monsters.json` (初始為 `{}`)
- [ ] `floors.json` (初始為 `{}`)
- [ ] `monster_groups.json` (初始為 `{}`)
- [ ] `reward_packages.json` (初始為 `{}`)

---
完成所有上述任務後，請勾選並更新 `RPG_IMPLEMENTATION_PLAN.md` 中的相應條目。 