# RPG系統公式求值引擎詳解

本文檔詳細描述了Gacha RPG系統中用於解析和計算JSON配置文件中定義的各種公式 (`*_formula`) 的求值引擎的設計。

## 1. 核心職責與目標

公式求值引擎的核心職責是安全、高效、準確地計算基於字符串的公式，這些公式廣泛應用於技能效果、被動觸發機率、傷害修正等方面。

**設計目標：**

*   **數據驅動：** 遊戲邏輯的數值部分由JSON中的公式定義，而非硬編碼。
*   **安全性：** 引擎必須防止任何形式的任意代碼執行，只能解析預定義的變量、運算符和函數。
*   **表達能力：** 支持足夠的數學運算和邏輯判斷，以滿足遊戲設計需求。
*   **可擴展性：** 易於添加新的變量、函數或運算符。
*   **性能：** 儘管在戰鬥中可能頻繁調用，但單個公式的計算應足夠快。
*   **易用性：** 公式語法應直觀易懂，方便策劃人員配置。

## 2. 支持的語法元素

### 2.1. 變量 (Variables)

公式中可以使用的變量由戰鬥上下文動態提供。所有可用的變量名及其含義應在 `RPG_08_Glossary.md` 或特定於變量上下文的文檔中明確列出。

**常見變量示例 (具體可用性取決於公式所在的上下文)：**

*   **施法者/擁有者相關 (Caster/Owner Context):**
    *   `caster_star_level`: 卡牌星級。
    *   `caster_rpg_level`: 卡牌RPG等級。
    *   `skill_level`: (通用)技能等級。
    *   `caster_stat_hp`: 施法者當前生命值。
    *   `caster_stat_max_hp`: 施法者最大生命值。
    *   `caster_stat_mp`: 施法者當前法力值。
    *   `caster_stat_max_mp`: 施法者最大法力值。
    *   `caster_stat_patk`: 施法者物理攻擊力。
    *   `caster_stat_pdef`: 施法者物理防禦力。
    *   `caster_stat_matk`: 施法者魔法攻擊力。
    *   `caster_stat_mdef`: 施法者魔法防禦力。
    *   `caster_stat_spd`: 施法者速度。
    *   `caster_stat_crit_rate`: 施法者暴擊率。
    *   `caster_stat_crit_dmg_multiplier`: 施法者暴擊傷害倍率。
    *   `caster_stat_accuracy`: 施法者命中率。
    *   `caster_stat_evasion`: 施法者閃避率。
    *   `caster_current_hp_percent`: 施法者當前生命值百分比 (0.0 - 1.0)。
    *   `caster_missing_hp_percent`: 施法者已損失生命值百分比 (0.0 - 1.0)。
    *   `caster_buff_stacks_<buff_id>`: 施法者身上某特定Buff的層數。

*   **目標相關 (Target Context，通常在效果計算時)：**
    *   `target_stat_hp`: 目標當前生命值。
    *   `target_stat_max_hp`: 目標最大生命值。
    *   `target_current_hp_percent`: 目標當前生命值百分比 (0.0 - 1.0)。
    *   `target_missing_hp_percent`: 目標已損失生命值百分比 (0.0 - 1.0)。
    *   `target_stat_pdef`: 目標物理防禦力。
    *   `target_stat_mdef`: 目標魔法防禦力。
    *   `target_is_boss`: 目標是否為Boss (0 或 1)。
    *   `target_debuff_stacks_<debuff_id>`: 目標身上某特定Debuff的層數。

*   **事件相關 (Event Context，通常在被動觸發條件判斷或效果計算時)：**
    *   `event_damage_dealt`: 本次事件中造成的傷害量。
    *   `event_heal_amount`: 本次事件中治療的量。
    *   `event_is_crit`: 本次事件是否暴擊 (0 或 1)。

*   **通用/常量:**
    *   `PI`: 圓周率。

**注意：** 變量名應遵循一致的命名規範，例如使用小寫字母和下劃線。

### 2.2. 數字 (Numbers)

支持整數 (e.g., `10`, `-5`) 和浮點數 (e.g., `0.5`, `1.25`)。

### 2.3. 運算符 (Operators)

按標準優先級排列：

1.  `()`: 圓括號，用於改變運算順序。
2.  `^`: 冪運算 (右結合)。例如 `2^3^2` 等於 `2^(3^2)` = `2^9` = `512`。
3.  `*`: 乘法。
4.  `/`: 除法。
5.  `%`: 取模 (取餘)。
6.  `+`: 加法。
7.  `-`: 減法。
8.  `>`: 大於。
9.  `<`: 小於。
10. `>=`: 大於等於。
11. `<=`: 小於等於。
12. `==`: 等於。
13. `!=`: 不等於。
14. `&&` 或 `and`: 邏輯與。
15. `||` 或 `or`: 邏輯或。

邏輯運算符 (`>`, `<`, `>=`, `<=`, `==`, `!=`, `&&`, `||`) 的結果為 `1` (真) 或 `0` (假)。

### 2.4. 函數 (Functions)

函數調用格式為 `function_name(argument1, argument2, ...)`。

*   **數學函數：**
    *   `min(a, b, ...)`: 返回參數中的最小值。
    *   `max(a, b, ...)`: 返回參數中的最大值。
    *   `abs(x)`: 返回 `x` 的絕對值。
    *   `floor(x)`: 返回不大於 `x` 的最大整數。
    *   `ceil(x)`: 返回不小於 `x` 的最小整數。
    *   `round(x)`: 返回 `x` 四捨五入到最近的整數。
    *   `sqrt(x)`: 返回 `x` 的平方根 (若 `x < 0`，行為需定義，如返回0或報錯)。
    *   `pow(base, exponent)`: 返回 `base` 的 `exponent` 次方 (同 `base ^ exponent`)。
    *   `ln(x)`: 返回 `x` 的自然對數。
    *   `log10(x)`: 返回 `x` 的以10為底的對數。
    *   `log(x, base)`: 返回 `x` 的以 `base` 為底的對數。
    *   `clamp(value, min_val, max_val)`: 將 `value` 限制在 `[min_val, max_val]` 區間內。如果 `value < min_val`，返回 `min_val`；如果 `value > max_val`，返回 `max_val`；否則返回 `value`。

*   **邏輯函數：**
    *   `if(condition, value_if_true, value_if_false)`: 如果 `condition` 計算結果為非零 (真)，則返回 `value_if_true`，否則返回 `value_if_false`。

*   **隨機函數 (慎用，需考慮可復現性)：**
    *   **注意：** 戰鬥相關的隨機性應由戰鬥引擎的RNG核心控制，公式引擎中的隨機函數通常不推薦用於影響戰鬥結果的隨機判定，除非該隨機結果在效果定義階段就已確定且不隨戰局改變。如果使用，必須確保其使用的隨機種子與戰鬥引擎的 `rng_seed` 關聯，以保證可復現性。
    *   **【第一階段實現備註】**：為簡化實現並集中管理隨機性，**公式引擎在第一階段不直接支持 `rand_int` 及 `rand_float` 函數**。所有必要的隨機事件（如暴擊、閃避等）應由核心戰鬥邏輯結合戰鬥隨機種子 (`rng_seed`) 處理，其結果可以作為布爾值（0或1）變量傳遞給公式引擎使用（例如 `event_is_crit`）。

## 3. 解析與求值過程

引擎內部通常採用以下步驟：

1.  **詞法分析 (Lexical Analysis / Tokenization):** 將公式字符串分解為一系列的詞法單元 (tokens)，如數字、變量名、運算符、函數名、括號等。
2.  **語法分析 (Syntax Analysis / Parsing):** 根據預定義的語法規則，將詞法單元流轉換為抽象語法樹 (AST) 或其他中間表示形式。此階段會檢查語法錯誤。
3.  **求值 (Evaluation):** 遍歷AST，從葉節點開始，根據運算符和函數的定義，結合傳入的變量上下文，計算出最終結果。
    *   變量替換：從上下文中查找變量的值。
    *   函數調用：執行函數邏輯。
    *   運算執行：執行算術和邏輯運算。

## 4. 錯誤處理

引擎需要能夠處理以下類型的錯誤：

*   **解析時錯誤 (Parse-time errors):**
    *   **詞法錯誤：** 無法識別的字符或符號。
    *   **語法錯誤：** 公式不符合語法規則，例如括號不匹配、運算符使用錯誤、函數參數數量不對等。
    *   處理方式：記錄錯誤，公式計算結果可視為一個預定義的錯誤值 (如 `NaN` 或 `0`)，或者向上拋出異常。

*   **求值時錯誤 (Evaluation-time errors):**
    *   **變量未定義：** 公式中使用了上下文中未提供的變量。
        *   處理方式：可以默認為0，或者記錄錯誤並返回錯誤值/拋出異常。
    *   **除零錯誤：** 嘗試除以零。
        *   處理方式：記錄錯誤，返回一個特殊值 (如 `Infinity` 或 `0`) 或拋出異常。
    *   **函數參數錯誤：** 傳遞給函數的參數類型或範圍不正確 (例如 `sqrt(-1)`)。
        *   處理方式：函數內部定義其錯誤行為，記錄錯誤，返回錯誤值或拋出異常。
    *   **類型不匹配：** 進行了不兼容類型的運算 (例如數字與無法轉換為數字的字符串相加，儘管本設計中變量主要為數值)。

**建議：** 對於遊戲邏輯，求值時錯誤通常應有一個明確的降級處理（例如，將無效的傷害加成視為0），並記錄詳細日誌供調試，而不是讓整個戰鬥崩潰。

## 5. 安全性考量

*   **無任意代碼執行：** 引擎的核心是只解釋和執行預定義的、安全的運算符和函數。絕不應使用如 `eval()` (Python) 或類似的可以直接執行任意宿主語言代碼的機制。
*   **變量沙箱：** 公式只能訪問由宿主代碼明確提供的變量上下文，不能訪問或修改外部系統的任意數據。
*   **資源限制 (可選)：** 對於非常複雜的公式或深度嵌套的函數調用，可以考慮設置計算步數或時間限制，以防止惡意構造的公式導致性能問題 (DoS)。但對於當前RPG系統的規模，可能不是首要問題。

## 6. 實現說明與上下文傳遞

*   **上下文對象：** 在調用公式引擎時，需要傳遞一個上下文對象 (例如字典或特定類的實例)，其中包含了當前公式可用的所有變量及其值。
*   **緩存 (可選)：** 對於完全由靜態值（如技能等級、星級，而非動態的戰鬥數據如當前HP）決定的公式，並且其AST結構固定的情況，可以考慮緩存AST甚至預計算結果，以提高性能。但需要仔細管理緩存的有效性。
*   **【推薦實現庫】**：為了在確保安全（避免直接使用`eval()`）的前提下簡化實現，推薦使用像 `asteval` 這樣的第三方Python庫。`asteval` 使用Python的 `ast` 模組進行安全的表達式解析與求值，允許精確控制可用的函數和變量。採用此類庫可以讓AI開發者專注於正確準備上下文變量和處理求值結果，而非從頭編寫複雜的解析器。無論使用何種庫，本文件定義的公式語法、可用變量和函數應作為核心設計遵循。

## 7. 示例公式

*   **傷害計算基礎值：** `caster_stat_patk * (1 + skill_level * 0.1)`
*   **被動觸發機率：** `clamp(0.2 + star_level * 0.05 + caster_stat_crit_rate * 0.1, 0.0, 1.0)`
*   **條件傷害加成：** `if(target_current_hp_percent < 0.3, 0.25, 0)` (如果目標血量低於30%，則傷害加成25%，否則為0)
*   **基於雙方速度差的閃避調整：** `max(0, (caster_stat_spd - target_stat_spd) * 0.001)`
*   **0.5 + (star_level * 0.02)**: 基礎值0.5，加上卡牌培養星級每級提供0.02的加成。
*   **min(caster_stat_patk * 1.5, target_stat_current_hp * 0.3)**: 取施法者物理攻擊1.5倍和目標當前生命30%中的較小值。
*   **(caster_stat_spd / target_stat_spd) * 0.1 + skill_level * 0.05**: 根據雙方速度差調整基礎值，並受通用技能等級加成。

---

本文檔為公式求值引擎的設計提供了詳細指導。在具體實現時，可以選擇現有的安全表達式求值庫 (如果找到合適的)，或者自研一個輕量級的解析器和求值器。 