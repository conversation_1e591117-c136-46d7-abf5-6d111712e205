# RPG 任務 05：屬性計算器

本文檔詳細列出了 RPG 系統中 `AttributeCalculator` 服務的實現任務。此服務負責根據基礎配置、RPG 等級、培養星級和星級效果來計算戰鬥單位的完整屬性。

**參考設計文檔：**
*   `RPG_05_Core_Runtime_Logic.md` (1. AttributeCalculator)
*   `RPG_02_Configuration_Files.md` (特別是 `cards.json` 的 `base_stats`, `growth_per_rpg_level` 和 `star_level_effects.json`)
*   `RPG_04_Domain_Model_Battle_System.md` (`Combatant` 模型，了解哪些屬性需要計算)
*   `RPG_08_Glossary.md` (術語定義)

**目標位置：** `rpg_system/battle_system/services/attribute_calculator.py`

## 1. AttributeCalculator 服務定義

在 `rpg_system/battle_system/services/attribute_calculator.py` 中創建 `AttributeCalculator` 類。

- [ ] **創建 `AttributeCalculator` 類：**
    - [ ] `__init__(self)`: (可能不需要特殊初始化，或用於注入依賴)
    - [ ] **主要方法：** `calculate_attributes(self, combatant_definition_id: str, combatant_type: Literal["CARD", "MONSTER"], rpg_level: int, star_level: int, config_loader: 'ConfigLoader') -> Dict[str, float]`
        - [ ] **參數說明：**
            - `combatant_definition_id`: 卡牌ID (`card_id`) 或怪物ID (`monster_id`)。
            - `combatant_type`: "CARD" 或 "MONSTER"，用於決定從哪個配置中讀取基礎數據。
            - `rpg_level`: 戰鬥單位的 RPG 等級。
            - `star_level`: 戰鬥單位的培養星級 (0-35)。
            - `config_loader`: `ConfigLoader` 實例，用於獲取配置數據。
        - [ ] **返回值：** 一個字典，包含所有計算後的戰鬥屬性 (e.g., `{"max_hp": 1000.0, "patk": 150.0, ...}`)。
            *   **核心屬性列表 (參考 `RPG_02_Configuration_Files.md` - `monsters.json` 和 `cards.json` -> `base_stats`):**
                *   `max_hp`
                *   `max_mp`
                *   `mp_regen_per_turn` (怪物通常固定，卡牌可能沒有或通過其他方式獲得)
                *   `patk` (物理攻擊)
                *   `pdef` (物理防禦)
                *   `matk` (魔法攻擊)
                *   `mdef` (魔法防禦)
                *   `spd` (速度)
                *   `crit_rate` (暴擊率)
                *   `crit_dmg_multiplier` (暴擊傷害倍率)
                *   `accuracy` (命中率)
                *   `evasion` (閃避率)
            *   還應包括那些通過星級效果增加的額外屬性 (如果設計文檔中有定義，例如 `fire_dmg_bonus`, `healing_received_increase` 等)。

## 2. 屬性計算邏輯實現 (`calculate_attributes` 方法)

- [ ] **步驟 1: 獲取基礎配置**
    - [ ] `if combatant_type == "CARD":`
        - [ ] `card_config = config_loader.get_card_config(combatant_definition_id)`
        - [ ] 如果 `card_config` 為 `None`，記錄錯誤並返回空字典或拋出異常。
        - [ ] `base_stats_config = card_config.base_stats`
        - [ ] `growth_config = card_config.growth_per_rpg_level`
        - [ ] `star_effects_key = card_config.star_level_effects_key`
    - [ ] `elif combatant_type == "MONSTER":`
        - [ ] `monster_config = config_loader.get_monster_config(combatant_definition_id)`
        - [ ] 如果 `monster_config` 為 `None`，記錄錯誤並返回空字典或拋出異常。
        - [ ] `base_stats_config = monster_config` (直接從怪物配置中取，注意怪物配置的屬性名可能與 `cards.json` 的 `BaseStats` 模型中的字段名完全對應)
        - [ ] `growth_config = None` (怪物通常沒有 RPG 等級成長)
        - [ ] `star_effects_key = None` (怪物通常沒有星級效果)
    - [ ] `else:`
        - [ ] 記錄未知 `combatant_type` 錯誤，返回空字典或拋出異常。
    - [ ] 初始化 `calculated_attributes = {}` 字典，將 `base_stats_config` 中的所有屬性複製到其中。

- [ ] **步驟 2: 計算 RPG 等級成長 (僅對卡牌)**
    - [ ] `if combatant_type == "CARD" and growth_config:`
        - [ ] `effective_rpg_level = rpg_level - 1` (通常基礎屬性是1級時的屬性，成長從2級開始計算)
        - [ ] `if effective_rpg_level > 0:`
            - [ ] 遍歷 `growth_config` 中的每個屬性 (e.g., `hp_growth`, `patk_growth`)。
            - [ ] `calculated_attributes[attribute_name] += growth_config[attribute_name_growth] * effective_rpg_level`
            - [ ] (例如：`calculated_attributes["max_hp"] += growth_config.hp * effective_rpg_level`)

- [ ] **步驟 3: 應用星級效果 (僅對卡牌且有 `star_effects_key`)**
    - [ ] `if combatant_type == "CARD" and star_effects_key and star_level > 0:`
        - [ ] `star_level_effects_config_group = config_loader.get_star_level_effects_config(star_effects_key)`
        - [ ] 如果 `star_level_effects_config_group` 為 `None`，記錄錯誤並繼續 (或警告)。
        - [ ] `else:`
            - [ ] 遍歷從 1 到 `star_level` 的每個星級 (或者設計文檔中 `star_level_effects.json` 的鍵是字符串 "1", "2", ... "35")。
            - [ ] `current_star_effect_config = star_level_effects_config_group.get(str(s_level))` (獲取當前遍歷到的星級 `s_level` 的效果配置)
            - [ ] `if current_star_effect_config:`
                - [ ] **應用扁平值 (flat) 屬性加成：**
                    - [ ] `if current_star_effect_config.additional_stats_flat:`
                        - [ ] `for stat_name, value in current_star_effect_config.additional_stats_flat.items():`
                            - [ ] `calculated_attributes[stat_name] = calculated_attributes.get(stat_name, 0) + value`
                - [ ] **應用百分比 (%) 屬性加成：**
                    - [ ] `if current_star_effect_config.additional_stats_percent:`
                        - [ ] `for stat_name, percent_value in current_star_effect_config.additional_stats_percent.items():`
                            - [ ] `base_value_for_percent_calc = base_stats_config.get(stat_name, 0)` (百分比加成通常基於基礎屬性，需確認設計)
                                *   **確認：** 百分比加成是基於 `base_stats_config` (卡牌1級時的基礎屬性) 還是基於已計算 RPG 等級後的屬性？設計文檔 `RPG_05` 提到 "基於卡牌自身1級時的基礎屬性"。
                            - [ ] `calculated_attributes[stat_name] += base_value_for_percent_calc * percent_value`
                - [ ] 其他星級效果 (如解鎖被動槽) 不在此計算器中處理，此處僅關注屬性。

- [ ] **步驟 4: 最終處理與返回**
    - [ ] 確保所有核心屬性都存在於 `calculated_attributes` 中，如果缺少則給予默認值 (e.g., 0)。
    - [ ] (可選) 進行屬性下限/上限校驗 (e.g., HP不應為負，暴擊率在0-1之間)。
    - [ ] 返回 `calculated_attributes` 字典。

## 3. 輔助方法 (如果需要)

- [ ] 可以考慮將部分重複邏輯 (如獲取配置、應用單個星級效果) 提取到私有輔助方法中。

## 4. 單元測試

為 `AttributeCalculator` 創建單元測試。

- [ ] **測試文件位置：** (例如 `tests/rpg_system/battle_system/services/test_attribute_calculator.py`)
- [ ] **Mock `ConfigLoader`：** 測試時需要 mock `ConfigLoader` 及其 `get_..._config` 方法，以返回預設的測試用配置數據。
- [ ] **測試用例：**
    - [ ] **卡牌屬性計算：**
        - [ ] 測試 1 級卡牌 (無星級) 的屬性是否等於其 `base_stats`。
        - [ ] 測試卡牌 RPG 等級成長 (e.g., 10級卡牌，無星級)。
        - [ ] 測試卡牌星級效果：
            - [ ] 只有扁平屬性加成。
            - [ ] 只有百分比屬性加成 (驗證百分比計算基準是否正確)。
            - [ ] 混合扁平與百分比加成。
            - [ ] 多個星級效果疊加。
        - [ ] 測試卡牌同時有 RPG 等級成長和星級效果。
    - [ ] **怪物屬性計算：**
        - [ ] 測試怪物屬性是否直接從其配置中讀取 (通常無 RPG 等級成長或星級)。
    - [ ] **邊界情況與錯誤處理：**
        - [ ] `combatant_definition_id` 無效 (預期行為，如返回空字典或拋出異常)。
        - [ ] `combatant_type` 無效。
        - [ ] `star_level_effects_key` 指向不存在的配置。
        - [ ] 配置數據不完整或格式錯誤 (部分由 Pydantic 處理，但計算器應能應對 `None` 的情況)。
    - [ ] **特定屬性驗證：**
        - [ ] 確保所有預期的屬性鍵都出現在返回結果中。

## 5. 文檔與類型提示

- [ ] 在 `AttributeCalculator` 類及其方法中添加清晰的文檔字符串 (docstrings)。
- [ ] 使用 `from typing import Dict, Literal` 等進行類型提示。
- [ ] 處理循環導入問題：對 `ConfigLoader` 使用字符串類型提示 `'ConfigLoader'`。
    ```python
    from typing import TYPE_CHECKING
    if TYPE_CHECKING:
        from rpg_system.config.loader import ConfigLoader
    ```

---
完成所有上述任務後，請勾選並更新 `RPG_IMPLEMENTATION_PLAN.md` 中的相應條目。 