**VII. 被動技能與天賦觸發器 (PassiveTriggerHandler)**

為了處理被動技能和天賦的觸發與應用，領域層將引入一個關鍵組件：`PassiveTriggerHandler`。

*   **職責:**
    *   集中管理和執行所有被動技能（包括卡牌天賦和裝備的通用被動）的觸發邏輯。
    *   根據戰鬥過程中發生的不同事件（如戰鬥開始、回合開始、受到傷害、造成傷害等），檢查相關戰鬥單位身上是否有被動技能滿足觸發條件。
    *   如果滿足條件（包括機率和附加條件），則根據被動技能的定義確定效果目標，並調用 `EffectApplier` 來應用具體效果。

*   **核心方法 (概念性):**
    *   `check_and_apply_passives(event_type, event_data, involved_combatants, battle_context)`:
        *   `event_type` (枚舉/字符串): 標識當前發生的戰鬥事件，如 `ON_DAMAGE_TAKEN`, `ON_TURN_START` 等。這些事件類型直接對應被動技能JSON定義中的 `trigger_condition.type`。
        *   `event_data` (字典): 包含與事件相關的詳細信息，例如 `{ "attacker": combatant_attacker, "target": combatant_target, "damage_dealt": 100, "skill_used": skill_X }`。這些數據用於評估被動技能的 `trigger_condition.additional_conditions` 和確定 `target_override`。
        *   `involved_combatants` (列表): 通常是與事件直接相關的戰鬥單位（例如，受擊者、攻擊者、行動者）。`PassiveTriggerHandler` 會檢查這些單位身上的被動技能。
        *   `battle_context` (對象): 提供戰場的全局信息，用於條件判斷。

*   **工作流程:**
    1.  **事件鉤子 (Hooks):** 在戰鬥引擎 (`Battle` 類) 的關鍵邏輯點（如計算完傷害後、回合開始時、行動執行後等），會調用 `PassiveTriggerHandler.check_and_apply_passives` 並傳入相應的事件類型和數據。
    2.  **遍歷與檢查:** `PassiveTriggerHandler` 會遍歷 `involved_combatants` 中的每個單位，獲取其所有天賦和已裝備的通用被動技能。
    3.  **匹配觸發條件:** 對於每個被動技能的每個 `PassiveEffectBlock`，檢查其 `trigger_condition.type` 是否與當前 `event_type` 匹配。
    4.  **評估機率與附加條件:** 如果類型匹配，則使用 `event_data` 和 `battle_context`（以及技能等級/星級）評估 `chance_formula` 和 `additional_conditions`。
    5.  **確定目標:** 如果所有條件滿足，根據 `target_override` 和 `event_data` 確定最終的目標單位列表。
    6.  **應用效果:** 對於每個選中的目標，調用 `EffectApplier.apply_effect()` 來應用 `effect_definitions` 中的每個效果。此處的施法者 (`caster_of_passive`) 是指擁有該被動技能的單位。

*   **被動觸發順序與優先級 (Order and Priority):**
    *   當單個戰鬥事件 (`event_type`) 可能同時觸發多個被動技能（來自同一個戰鬥單位的天賦和多個通用被動，或來自不同戰鬥單位的被動）時，它們的結算順序至關重要。系統採用以下優先級規則：
        1.  **優先級分組：** 首先，按照被動技能的來源和類型確定大的優先級組別。
            *   **天賦被動技能 (Innate Passive Skills):** 擁有最高的基礎優先級。
            *   **通用被動技能 (Common Passive Skills):** 基礎優先級低於天賦被動。
        2.  **明確的優先級配置 (通用被動)：**
            *   對於通用被動技能 (`passive_skills.json`)，其定義中可以包含一個可選的整數字段 `trigger_priority` (建議默認為 `0`)。數值越大，代表優先級越高。
        3.  **排序規則：**
            *   `PassiveTriggerHandler` 在收集到所有可能被當前事件觸發的被動效果塊後，將按照以下順序進行排序和執行：
                *   首先，比較優先級組別：天賦被動優先於通用被動。
                *   在通用被動技能內部，比較它們的 `trigger_priority` 值，值大的優先。
                *   如果 `trigger_priority` 值相同（或均未配置），則可以按照被動技能的 `skill_id` 進行字母順序排序，以確保一個確定性的執行順序。
        4.  **執行：** 按照排序後的順序，依次執行每個滿足觸發條件的被動效果。前一個被動效果的執行結果可能會影響後一個被動技能的觸發條件或效果計算（例如，改變了某個戰鬥單位的HP）。
    *   **配置影響：**
        *   設計者在配置 `passive_skills.json` 時，應考慮 `trigger_priority` 的設置，以控制複雜情況下被動技能的互動。
        *   `innate_passive_skills.json` 中的天賦被動無需配置 `trigger_priority`，它們默認優先。

*   **擴展性:**
    *   **新觸發類型:** 在JSON中定義新的 `trigger_condition.type`，並在戰鬥流程中增加相應的鉤子調用 `PassiveTriggerHandler`。
    *   **新附加條件:** 在JSON中定義新的 `additional_conditions.check` 類型，並在 `PassiveTriggerHandler` 的條件評估邏輯中增加對應處理。
    *   **新效果:** 依然是擴展 `EffectApplier` 和 `effect_definitions.json`。

通過這種方式，絕大部分被動技能的邏輯差異體現在JSON配置中，PassiveTriggerHandler 保持了通用性，而具體的效果應用則複用了現有的 `EffectApplier`。 