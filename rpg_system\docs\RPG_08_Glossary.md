# RPG系統術語表 (Glossary)

本文件定義了Gacha RPG系統中使用的核心術語，以確保文檔和團隊溝通的一致性。

## 核心概念

*   **卡牌培養星級 (Card Cultivation Star Level):**
    *   **定義：** 指玩家對所擁有卡牌進行培養後達到的等級，存儲於數據庫 (例如 `gacha_user_collections.star_level`)。
    *   **影響：** 主要影響卡牌的天賦被動技能 (`innate_passive_skill_id`) 的效果 (對應 `innate_passive_skills.json` 中 `effects_by_star_level` 的鍵)，以及 `star_level_effects.json` 中定義的額外屬性加成或解鎖。這些配置文件中的效果鍵直接使用培養星級的特定數值。
    *   **範圍：** 例如 0-35級。0級代表初始狀態。
    *   **公式變量示例：** `star_level`, `caster_star_level` (指此0-35的培養星級)。

*   **卡牌/技能稀有度 (Card/Skill Rarity):**
    *   **定義：** 指卡牌本身或技能的固有品質、罕見程度或評級 (例如 N, R, SR, SSR, UR 的體現)。
    *   **影響：** 主要影響卡牌的獲取難度、初始潛力 (如生成 `cards.json` 時基礎屬性及成長值的範圍)、可學習/綁定的天賦技能的品質、以及其他非戰鬥數值。
    *   **範圍：** 例如 1-7 (7代表最稀有/最高品質)。
    *   **注意：** 此稀有度與「卡牌培養星級」是獨立的概念，不直接用於 `effects_by_star_level` 的鍵。

*   **RPG等級 (RPG Level / Card RPG Level):**
    *   **定義：** 指卡牌在RPG戰鬥系統中的等級，通過戰鬥獲得經驗值提升。
    *   **影響：** 主要影響卡牌的基礎戰鬥屬性，根據 `cards.json` 中定義的 `base_stats` 和 `growth_per_rpg_level` 計算得出。
    *   **範圍：** 例如 1-100級。
    *   **公式變量示例：** `caster_rpg_level`, `target_rpg_level`

*   **技能等級 (Skill Level / Global Skill Proficiency Level):**
    *   **定義：** 指玩家賬號下，對某個特定 `skill_id` (通用主動技能或通用被動技能) 所擁有的全局熟練度等級。此等級存儲在 `gacha_user_learned_global_skills` 表中，並通過消耗特定資源提升。
    *   **影響：** 當任何卡牌裝備了該 `skill_id` 的技能時，其在戰鬥中生效的等級即為此全局熟練度等級 (效果通常對應技能配置文件中 `effects_by_level` 的相應等級定義)。其效果公式中的 `skill_level` 變量指此全局熟練度等級。
    *   **範圍：** 例如 1 至該技能配置的 `max_level`。
    *   **公式變量示例：** `skill_level` (當計算通用技能效果時，指全局熟練度等級)

*   **效果模板 (Effect Template):**
    *   **定義：** 在 `effect_templates.json` 中預先定義好的可複用效果配置。
    *   **目的：** 簡化技能和狀態效果的配置，提高一致性。
    *   **使用：** 在技能的 `effect_definitions` 中通過 `effect_template: "TEMPLATE_NAME"` 引用，並可選擇性覆蓋模板中的參數。

*   **效果定義 (Effect Definition):**
    *   **定義：** 描述一個具體遊戲效果（如造成傷害、治療、施加狀態）的JSON對象。可以通過引用效果模板並覆蓋參數，或者直接指定 `effect_type` 和相關參數來定義。
    *   **位置：** 出現在技能定義的 `effect_definitions` 數組中，以及狀態效果定義的各個觸發階段（如 `effect_definitions_on_apply`, `effect_definitions_per_tick`）中。

*   **事件類型 (Event Type):**
    *   **定義：** 標識戰鬥過程中發生的特定情況或動作的字符串。例如 `ON_DAMAGE_TAKEN`, `ON_TURN_START`。
    *   **用途：** 主要用於被動技能 (`PassiveEffectBlock`) 的 `trigger_condition.type`，以決定被動技能的觸發時機。
    *   **關聯數據：** 每種事件類型通常會附帶一組 `event_data`，包含該事件的上下文信息（如攻擊者、目標、傷害值等）。

## 屬性 (Stats)

*   **基礎屬性 (Base Stats):**
    *   定義：在 `cards.json` 中定義的卡牌1級時的各項RPG屬性值（如 `hp`, `patk` 等）。
*   **成長值 (Growth per RPG Level):**
    *   定義：在 `cards.json` 中定義的卡牌每提升1級RPG等級所增加的屬性值。
*   **戰鬥屬性 (Combat Stats):**
    *   定義：戰鬥單位在戰鬥中實際使用的屬性，由基礎屬性、RPG等級成長、星級效果、技能效果、狀態效果等多方面疊加計算得出。

## 技能相關

*   **天賦被動技能 (Innate Passive Skill):**
    *   定義：卡牌固有的被動技能，定義在 `innate_passive_skills.json`。其效果強度通常隨卡牌的「卡牌培養星級」提升 (效果定義在 `effects_by_star_level` 中，鍵為培養星級的特定數值)。
*   **通用主動技能 (Active Skill):**
    *   定義：玩家可以通過商店購買或卡牌間轉移獲得其 `skill_id` 並裝備給卡牌的主動釋放技能，定義在 `active_skills.json`。其效果強度隨玩家對該 `skill_id` 的全局「技能熟練度等級」提升。可以通過 `target_logic_details` 配置複雜的目標選擇規則。
*   **通用被動技能 (Common Passive Skill):**
    *   定義：玩家可以通過商店購買或卡牌間轉移獲得其 `skill_id` 並裝備給卡牌的被動技能，定義在 `passive_skills.json`。其效果強度隨玩家對該 `skill_id` 的全局「技能熟練度等級」提升。
*   **普攻技能 (Primary Attack Skill):**
    *   定義：卡牌的普通攻擊，通常也是一個定義在 `active_skills.json` 中的技能，但具有不消耗MP、無初始冷卻的特性。其生效等級也可能參考玩家對該普攻 `skill_id` 的全局熟練度等級。

## 其他

*   **(待補充)**

---

**使用建議：**

*   在編寫新的RPG相關文檔或進行團隊討論時，請參考此術語表。
*   當引入新的核心概念或術語時，請及時更新此文檔。 